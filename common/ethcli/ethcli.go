package ethcli

import "C"
import (
	"context"
	"math/big"
	"math/rand"

	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/ethereum/go-ethereum/ethclient/gethclient"
	"github.com/ethereum/go-ethereum/rpc"
	"github.com/lmittmann/w3"
	"github.com/lmittmann/w3/module/eth"
	"github.com/lmittmann/w3/w3types"
	"github.com/pkg/errors"
	"golang.org/x/sync/errgroup"
)

type EthCli struct {
	urls    []string
	ethClis []*ethclient.Client
	rpcClis []*rpc.Client
	clis    []*w3.Client
}

func NewEthCli(urls []string) (*EthCli, error) {
	var (
		ethClis []*ethclient.Client
		clis    []*w3.Client
		rpcClis []*rpc.Client
	)
	for _, url := range urls {
		ethCli, err := ethclient.Dial(url)
		if err != nil {
			return nil, errors.WithMessage(err, "ethclient.Dial")
		}
		ethClis = append(ethClis, ethCli)
		clis = append(clis, w3.NewClient(ethCli.Client()))

		rpcCli, err := rpc.Dial(url)
		if err != nil {
			return nil, errors.WithMessage(err, "rpc.Dial")
		}
		rpcClis = append(rpcClis, rpcCli)
	}
	return &EthCli{
		urls:    urls,
		ethClis: ethClis,
		clis:    clis,
		rpcClis: rpcClis,
	}, nil
}

func (e *EthCli) OriginCli() *ethclient.Client {
	return e.ethClis[rand.Intn(len(e.ethClis))]
}

func (e *EthCli) RpcCli() *rpc.Client {
	return e.rpcClis[rand.Intn(len(e.rpcClis))]
}

func (e *EthCli) GethCli() *gethclient.Client {
	cli := e.RpcCli()
	return gethclient.New(cli)
}

func (e *EthCli) Cli() *w3.Client {
	return e.clis[rand.Intn(len(e.clis))]
}

func (e *EthCli) BlockNumber(ctx context.Context) (*big.Int, error) {
	var blockNumber big.Int
	call := eth.BlockNumber().Returns(&blockNumber)
	err := e.Cli().CallCtx(ctx, call)
	return &blockNumber, err
}

// BatchCall 批量调用，每次调用 100 个
func (e *EthCli) BatchCall(ctx context.Context, calls ...w3types.Caller) error {
	if len(calls) == 0 {
		return nil
	}
	var eg errgroup.Group
	batch := 80
	eg.SetLimit(500)
	for i := 0; i < len(calls); i += batch {
		start := i
		end := start + batch
		if end > len(calls) {
			end = len(calls)
		}
		eg.Go(func() error {
			return e.Cli().CallCtx(ctx, calls[start:end]...)
		})
	}
	return eg.Wait()
}

type CollectErr interface {
	SetErr(err error)
}

// BatchCallCollectErrs 批量调用，每次调用 100 个
func (e *EthCli) BatchCallCollectErrs(ctx context.Context, calls []w3types.Caller, collectErr []CollectErr, limit, batch int) error {
	if len(calls) == 0 {
		return nil
	}
	var (
		eg errgroup.Group
	)
	eg.SetLimit(limit)
	for i := 0; i < len(calls); i += batch {
		start := i
		end := start + batch
		if end > len(calls) {
			end = len(calls)
		}
		eg.Go(func() error {
			err := e.Cli().CallCtx(ctx, calls[start:end]...)
			callErrs, ok := err.(w3.CallErrors)
			if !ok {
				return err
			}
			for i, callErr := range callErrs {
				collectErr[start+i].SetErr(callErr)
			}
			return nil
		})
	}
	return eg.Wait()
}

func (e *EthCli) EstimateGas(ctx context.Context, msg *w3types.Message, blockNumber *big.Int) (uint64, error) {
	var gas uint64
	call := eth.EstimateGas(msg, blockNumber).Returns(&gas)
	if err := e.Cli().CallCtx(ctx, call); err != nil {
		return 0, errors.WithMessage(err, "EstimateGas")
	}
	return gas, nil
}
