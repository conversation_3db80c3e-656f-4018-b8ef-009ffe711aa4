package ethcli_test

import (
	"context"
	"math/big"
	"testing"

	"c1ay/flasharb/common/ethcli"

	"github.com/lmittmann/w3"
	"github.com/lmittmann/w3/w3types"
)

func TestEthCli_EstimateGas(t *testing.T) {
	cli, err := ethcli.NewEthCli([]string{"https://eth.bd.evmos.org:8545"})
	if err != nil {
		t.<PERSON>al(err)
	}
	ctx := context.Background()
	to := w3.A("******************************************")
	msg := w3types.Message{
		//From:  w3.A("******************************************"),
		To:    &to,
		Value: nil,
		Input: []byte("0xc30f62c300000000000000000000000000000000000000000000000000000000000000200000000000000000000000005aab8ac26f124441656c077a75db69410ea19e5b0000000000000000000000002c68d1d6ab986ff4640b51e1f14c716a076e44c40000000000000000000000000000000000000000000000000e4b4b8af6a700000000000000000000000000000000000000000000000000000001d463c80f3000000000000000000000000000000000000000000000000000000f8a5e58e6b06300000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000e4317f0f71597d5f3675ffd505ab887b05bfc61000000000000000000000000000000000000000000000000000000000000000422c68d1d6ab986ff4640b51e1f14c716a076e44c40001f48fa78ceb7f04118ec6d06aac37ca854691d8e963000bb82c68d1d6ab986ff4640b51e1f14c716a076e44c4000000000000000000000000000000000000000000000000000000000000"),
		//Func:  nil,
		//Args:  nil,
	}
	gas, err := cli.EstimateGas(ctx, &msg, big.NewInt(15505399))
	if err != nil {
		t.Fatal(err)
	}
	t.Log(gas)
}
