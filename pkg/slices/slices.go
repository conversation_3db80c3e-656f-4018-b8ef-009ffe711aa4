package slices

import "golang.org/x/exp/constraints"

// Map ...
func Map[T any, U any](s []T, f func(T) U) []U {
	if s == nil {
		return []U(nil)
	}
	if len(s) == 0 {
		return []U{}
	}
	r := make([]U, 0, len(s))
	for _, v := range s {
		r = append(r, f(v))
	}
	return r
}

// Reduce ...
func Reduce[T any, U any](s []T, f func(prev U, current T) U, init U) U {
	if len(s) == 0 {
		return init
	}
	prev := init
	for _, v := range s {
		prev = f(prev, v)
	}
	return prev
}

func Filter[T any](s []T, f func(T) bool) []T {
	if len(s) == 0 {
		return []T{}
	}
	r := make([]T, 0, len(s))
	for _, v := range s {
		if f(v) {
			r = append(r, v)
		}
	}
	return r
}

// <PERSON> takes the first N of the slice
func Head[T any](s []T, n int) []T {
	if len(s) < n {
		return s
	}
	return s[:n]
}

// Union 并集
func Union[T comparable](s1, s2 []T) []T {
	if len(s1) == 0 {
		return s2
	}
	if len(s2) == 0 {
		return s1
	}
	m := make(map[T]struct{}, len(s1)+len(s2))
	for _, v := range s1 {
		m[v] = struct{}{}
	}
	for _, v := range s2 {
		m[v] = struct{}{}
	}

	s := make([]T, 0, len(s1)+len(s2))
	for k := range m {
		s = append(s, k)
	}
	return s
}

// Intersect 交集
func Intersect[T comparable](s1, s2 []T) []T {
	set := make([]T, 0)
	for _, v := range s1 {
		if Contains(s2, v) {
			set = append(set, v)
		}
	}
	return set
}

// Difference 差集
func Difference[T comparable](s1, s2 []T) []T {
	set := make([]T, 0)
	intersect := Intersect(s1, s2)
	for _, v := range s1 {
		if !Contains(intersect, v) {
			set = append(set, v)
		}
	}
	return set
}

// Contains ...
func Contains[T comparable](s []T, v T) bool {
	for _, vv := range s {
		if vv == v {
			return true
		}
	}
	return false
}

// Reverse ...
func Reverse[T any](s []T) {
	first := 0
	last := len(s) - 1
	for first < last {
		s[first], s[last] = s[last], s[first]
		first++
		last--
	}
}

// Deduplicate ...
func Deduplicate[T comparable](s []T) []T {
	return DeduplicateBy(s, func(v T) T { return v })
}

// DeduplicateBy ...
func DeduplicateBy[T any, U comparable](s []T, f func(v T) U) []T {
	if len(s) <= 1 {
		return s
	}
	m := make(map[U]struct{}, len(s))
	s2 := make([]T, 0, len(m))
	for _, v := range s {
		if _, ok := m[f(v)]; ok {
			continue
		}
		m[f(v)] = struct{}{}
		s2 = append(s2, v)
	}
	return s2
}

func Max[T constraints.Ordered](s []T) T {
	if len(s) == 0 {
		var zero T
		return zero
	}
	m := s[0]
	for _, v := range s {
		if m < v {
			m = v
		}
	}
	return m
}

func Min[T constraints.Ordered](s []T) T {
	if len(s) == 0 {
		var zero T
		return zero
	}
	m := s[0]
	for _, v := range s {
		if m > v {
			m = v
		}
	}
	return m
}
