package log

import (
	"context"
	"os"
	"strings"

	"c1ay/flasharb/pkg/utils"

	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var (
	log     *zap.Logger
	textLog *zap.Logger
)

func init() {
	encoderConfig := zap.NewDevelopmentEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	encoder := zapcore.NewJSONEncoder(encoderConfig)

	consoleEncoder := zapcore.NewConsoleEncoder(encoderConfig)
	consoleDebugging := zapcore.Lock(os.Stdout)

	fileWriter := zapcore.AddSync(&lumberjack.Logger{
		Filename:   "./logs/info.log", //日志文件存放目录，如果文件夹不存在会自动创建
		MaxSize:    5,                 //文件大小限制,单位MB
		MaxBackups: 100,               //最大保留日志文件数量
		MaxAge:     30,                //日志文件保留天数
		Compress:   false,             //是否压缩处理
	})

	log = zap.New(
		zapcore.NewTee(
			zapcore.NewCore(encoder, fileWriter, zapcore.DebugLevel),
			zapcore.NewCore(consoleEncoder, consoleDebugging, zapcore.DebugLevel)),
		zap.AddCaller(),
	)

	lumberJackLogger := &lumberjack.Logger{
		Filename:   "./profit.log",
		MaxSize:    1,
		MaxBackups: 5,
		MaxAge:     30,
		Compress:   false,
	}
	writeSyncer := zapcore.AddSync(lumberJackLogger)
	textEncoderConfig := zap.NewProductionEncoderConfig()
	textEncoderConfig.EncodeTime = zapcore.RFC3339TimeEncoder
	textEncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	textEncoder := zapcore.NewConsoleEncoder(textEncoderConfig)
	core := zapcore.NewCore(textEncoder, writeSyncer, zapcore.DebugLevel)

	textLog = zap.New(core)
}

func Logger() *zap.Logger {
	return log
}

func With(fields ...zap.Field) *zap.Logger {
	return log.With(fields...)
}

func Context(ctx context.Context) *zap.Logger {
	l := utils.GetLogger(ctx)
	if l == nil {
		return log
	}
	return l
}

func SummaryTx(actualProfit, txProfit, txCost, coin string) {
	if !strings.HasPrefix(actualProfit, "-") {
		textLog.Sugar().Infof("[P 🤑] P/L: %s %s, TX profit: %s %s, TX cost: %s %s", coin, actualProfit, coin, txProfit, coin, txCost)
	} else {
		textLog.Sugar().Errorf("[L 😥] P/L: %s %s, TX profit: %s %s, TX cost: %s %s", coin, actualProfit, coin, txProfit, coin, txCost)
	}
}
