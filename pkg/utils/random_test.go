package utils_test

import (
	"c1ay/flasharb/pkg/utils"
	"testing"
)

func TestRandFloat(t *testing.T) {
	min := 0.1
	max := 0.2
	result := utils.RandFloat(min, max)
	if result < min || result > max {
		t.<PERSON><PERSON><PERSON>("RandFloat(%f, %f) = %f, want %f <= result <= %f", min, max, result, min, max)
	}
	result2 := utils.RandFloat(min, max)
	if result == result2 {
		t.<PERSON><PERSON><PERSON>("RandFloat(%f, %f) = %f, want result != result2", min, max, result)
	}
}
