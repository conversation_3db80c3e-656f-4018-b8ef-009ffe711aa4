package utils

import (
	"github.com/lmittmann/w3"
	"github.com/lmittmann/w3/w3types"
	"golang.org/x/sync/errgroup"
)

// BatchCall 批量调用
func BatchCall(cli *w3.Client, limit int, calls ...w3types.Caller) error {
	if len(calls) <= limit {
		return cli.Call(calls...)
	}
	var eg errgroup.Group
	eg.SetLimit(5000)
	for i := 0; i < len(calls); i += limit {
		start := i
		end := start + limit
		if end > len(calls) {
			end = len(calls)
		}
		eg.Go(func() error {
			return cli.Call(calls[start:end]...)
		})
	}
	return eg.Wait()
}
