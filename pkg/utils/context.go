package utils

import (
	"context"
	"time"

	"github.com/ethereum/go-ethereum/core/types"
	"go.uber.org/zap"
)

type ctxKey string

const (
	ctxKeyBlock   ctxKey = "Block"
	ctxKeyLogger  ctxKey = "Logger"
	ctxKeyStartAt ctxKey = "StartAt"
)

func getFromCtx[T any](ctx context.Context, key ctxKey) T {
	v, _ := ctx.Value(key).(T)
	return v
}

func setToCtx[T any](ctx context.Context, key ctxKey, v T) context.Context {
	return context.WithValue(ctx, key, v)
}

func SetBlock(ctx context.Context, block *types.Block) context.Context {
	return setToCtx(ctx, ctxKeyBlock, block)
}

func GetBlock(ctx context.Context) *types.Block {
	return getFromCtx[*types.Block](ctx, ctxKeyBlock)
}

func SetLogger(ctx context.Context, logger *zap.Logger) context.Context {
	return setToCtx(ctx, ctxKeyLogger, logger)
}

func GetLogger(ctx context.Context) *zap.Logger {
	return getFromCtx[*zap.Logger](ctx, ctxKeyLogger)
}

func SetStartAt(ctx context.Context, startAt time.Time) context.Context {
	return setToCtx[time.Time](ctx, ctxKeyStartAt, startAt)
}

func GetStartAt(ctx context.Context) time.Time {
	return getFromCtx[time.Time](ctx, ctxKeyStartAt)
}
