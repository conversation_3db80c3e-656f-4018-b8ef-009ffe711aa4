// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.0;

interface IArb {

    struct ArbParams {
        address token0;
        address token1;
        uint256 amount0;
        uint256 amount1;
        uint24 fee;

        uint256 minProfit;
        bytes path;
        address profitTo;
    }

    struct ArbV3Params {
        address pool;
        address borrowToken;
        uint256 borrowAmount;
        uint256 flashLoanFee;

        uint256 minProfit;
        bytes path;
        address profitTo;
    }

    function arb(ArbParams memory params) external;

    function arbSourceV3(ArbV3Params memory params) external;
    function arbSourceV2(ArbV3Params memory params) external;
}
