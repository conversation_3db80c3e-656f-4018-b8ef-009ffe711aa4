// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.19;

// import "forge-std/Test.sol";

/// @title Safe casting methods
/// @notice Contains methods for safely casting between types
library SafeCast {
    /// @notice Cast a uint256 to a uint160, revert on overflow
    /// @param y The uint256 to be downcasted
    /// @return z The downcasted integer, now type uint160
    function toUint160(uint256 y) internal pure returns (uint160 z) {
        require((z = uint160(y)) == y);
    }

    /// @notice Cast a int256 to a int128, revert on overflow or underflow
    /// @param y The int256 to be downcasted
    /// @return z The downcasted integer, now type int128
    function toInt128(int256 y) internal pure returns (int128 z) {
        require((z = int128(y)) == y);
    }

    /// @notice Cast a uint256 to a int256, revert on overflow
    /// @param y The uint256 to be casted
    /// @return z The casted integer, now type int256
    function toInt256(uint256 y) internal pure returns (int256 z) {
        require(y < 2 ** 255);
        z = int256(y);
    }
}

// helper methods for interacting with ERC20 tokens and sending ETH that do not consistently return true/false
library TransferHelper {
    function safeApprove(address token, address to, uint256 value) internal {
        // bytes4(keccak256(bytes('approve(address,uint256)')));
        (bool success, bytes memory data) = token.call(abi.encodeWithSelector(0x095ea7b3, to, value));
        require(
            success && (data.length == 0 || abi.decode(data, (bool))), "TransferHelper::safeApprove: approve failed"
        );
    }

    function safeTransfer(address token, address to, uint256 value) internal {
        if (value == 0) {
            return;
        }
        // bytes4(keccak256(bytes('transfer(address,uint256)')));
        (bool success, bytes memory data) = token.call(abi.encodeWithSelector(0xa9059cbb, to, value));
        require(
            success && (data.length == 0 || abi.decode(data, (bool))), "TransferHelper::safeTransfer: transfer failed"
        );
    }

    function safeTransferFrom(address token, address from, address to, uint256 value) internal {
        // bytes4(keccak256(bytes('transferFrom(address,address,uint256)')));
        (bool success, bytes memory data) = token.call(abi.encodeWithSelector(0x23b872dd, from, to, value));
        require(
            success && (data.length == 0 || abi.decode(data, (bool))),
            "TransferHelper::transferFrom: transferFrom failed"
        );
    }

    function safeTransferETH(address to, uint256 value) internal {
        (bool success,) = to.call{value: value}(new bytes(0));
        require(success, "TransferHelper::safeTransferETH: ETH transfer failed");
    }
}

interface IERC20 {
    event Approval(address indexed owner, address indexed spender, uint256 value);
    event Transfer(address indexed from, address indexed to, uint256 value);

    function name() external view returns (string memory);

    function symbol() external view returns (string memory);

    function decimals() external view returns (uint8);

    function totalSupply() external view returns (uint256);

    function balanceOf(address owner) external view returns (uint256);

    function allowance(address owner, address spender) external view returns (uint256);

    function approve(address spender, uint256 value) external returns (bool);

    function transfer(address to, uint256 value) external returns (bool);

    function transferFrom(address from, address to, uint256 value) external returns (bool);
}

interface IUniswapV3Pool {
    function swap(
        address recipient,
        bool zeroForOne,
        int256 amountSpecified,
        uint160 sqrtPriceLimitX96,
        bytes calldata data
    ) external returns (int256 amount0, int256 amount1);
}

interface ISovrynSwap {
    function convertByPath(
        address[] memory _path,
        uint256 _amount,
        uint256 _minReturn,
        address _beneficiary,
        address _affiliateAccount,
        uint256 _affiliateFee
    ) external payable returns (uint256);
    function rateByPath(address[] memory _path, uint256 _amount) external returns (uint256);
}

interface ISovrynPool {
    function reserveWeight(address _reserveToken) external view returns (uint32);
    function reserveBalance(address _reserveToken) external view returns (uint256);
}

// 在 Sovryn 和 UniswapV3Pool 之间套利
contract ArbV4 {
    using SafeCast for uint256;

    uint160 internal constant MIN_SQRT_RATIO = **********;
    uint160 internal constant MAX_SQRT_RATIO = 1461446703485210103287273052203988822378723970342;
    address private constant profitAddr = 0xe4317f0f71597D5f3675fFD505AB887b05bfC610;
    address constant caller1 = 0xf58A24eaA342639f62A2BF7f632362ABc639E4E5;
    address constant caller2 = 0xE35A4eF33104a5B725090752dAdEd78029bA215E;
    address constant caller3 = 0x77E18355BB9AA0A9e165dFEF33de295bC73FD3Da;
    address constant sovrynSwapNetwork = 0x98aCE08D2b759a265ae326F010496bcD63C15afc;

    address private owner;

    constructor(address _owner) {
        owner = _owner;
    }

    modifier onlyOwner() {
        require(msg.sender == owner, "NO");
        _;
    }

    modifier onlyCaller() {
        if (msg.sender != owner && msg.sender != caller1 && msg.sender != caller2 && msg.sender != caller3) {
            revert("NOC");
        }
        _;
    }

    struct ArbParams {
        address loanPool;
        address[] arbPath; // sovryn 中的 path
        address repayToken; // 偿还的token
        uint256 repayAmount; // 偿还的数量
        uint256 swapAmount; // swap数量
        address borrowToken; // 借贷的token
        uint256 borrowAmount; // 借贷的数量
        address profitToken; // profit token
    }

    function startArb(ArbParams memory params) public returns (uint256 profit) {
        // 1. 从 uniswap v3 pool 中借款 token1
        // 2. 在 sovryn pool 中将 token1 swap 成 token2
        // 3. 将 token2 还给 uniswap v3 pool
        // 4. 将利润转给 profitAddr
        unchecked {
            uint256 beforeBalance = IERC20(params.profitToken).balanceOf(profitAddr);
            bool zeroForOne = params.borrowToken < params.repayToken ? false : true;
            IUniswapV3Pool(params.loanPool).swap(
                address(this),
                zeroForOne,
                -params.borrowAmount.toInt256(), // 负数表示输出的数量
                zeroForOne ? MIN_SQRT_RATIO + 1 : MAX_SQRT_RATIO - 1,
                abi.encode(params)
            );
            profit = IERC20(params.profitToken).balanceOf(profitAddr) - beforeBalance;
            // console2.log("profit: ", profit);
        }
    }

    function uniswapV3SwapCallback(int256 amount0Delta, int256 amount1Delta, bytes calldata data) external {
        ArbParams memory params = abi.decode(data, (ArbParams));
        uint256 amountOut = ISovrynSwap(sovrynSwapNetwork).rateByPath(params.arbPath, params.swapAmount);
        // console2.log("amountOut:    ", amountOut);
        // console2.log("amount0Delta: ", amount0Delta);
        // console2.log("amount1Delta: ", amount1Delta);
        require(amountOut >= params.repayAmount, "NP");
        // params.repayAmount = amount0Delta > 0 ? uint256(amount0Delta) : uint256(amount1Delta);
        IERC20(params.borrowToken).approve(sovrynSwapNetwork, params.swapAmount);
        // swap
        ISovrynSwap(sovrynSwapNetwork).convertByPath(
            params.arbPath, params.swapAmount, params.repayAmount, address(this), address(0), 0
        );
        // repay
        TransferHelper.safeTransfer(params.repayToken, msg.sender, params.repayAmount);
        // profit
        TransferHelper.safeTransfer(params.profitToken, profitAddr, IERC20(params.profitToken).balanceOf(address(this)));
    }

    function withdrawToken(address token, address to, uint256 amount) public onlyOwner {
        TransferHelper.safeTransfer(token, to, amount);
    }

    function withdrawETH(address payable _to, uint256 amount) public onlyOwner {
        bool sent = _to.send(amount);
        require(sent, "WF");
    }

    receive() external payable {}
}
