// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "forge-std/Test.sol";

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

import "@uniswap/v3-core/contracts/interfaces/IUniswapV3Pool.sol";
import "@uniswap/v3-periphery/contracts/libraries/TransferHelper.sol";
import '@uniswap/v3-core/contracts/libraries/SafeCast.sol';


contract ArbV3 {
    using SafeCast for uint256;

    error NotOwner();
    error NotFromPool();


    uint160 internal constant MIN_SQRT_RATIO = **********;
    uint160 internal constant MAX_SQRT_RATIO = 1461446703485210103287273052203988822378723970342;

    address public owner;
    address constant private profitAddr = 0xe4317f0f71597D5f3675fFD505AB887b05bfC610;
    address constant private caller1 = 0xf58A24eaA342639f62A2BF7f632362ABc639E4E5;
    address constant private caller2 = 0xE35A4eF33104a5B725090752dAdEd78029bA215E;
    address constant private caller3 = 0x77E18355BB9AA0A9e165dFEF33de295bC73FD3Da;


    constructor(address _owner) {
        owner = _owner;
    }

    modifier onlyOwner() {
        if (msg.sender != owner) {
            revert NotOwner();
        }
        _;
    }

    modifier onlyCaller() {
        if (msg.sender != owner && msg.sender != caller1 && msg.sender != caller2 && msg.sender != caller3){
            revert("NOC");
        }
        _;
    }

    struct ArbParams {
        address pool;
        address repayToken; // 偿还的token
        uint256 repayAmount; // 偿还的数量
        address borrowToken; // 借贷的token
        uint256 borrowAmount; // 借贷的数量
        address[] pools; // 第二个池子到第n个池子 eg: (cake, usdt), (usdt, bnb)
        address[] path; // 第二个池子到第n个池子的路径 eg: cake->usdt->bnb
        uint256 blockNumber; // block.number
        bool loanSwap; // 是否是借贷swap
    }

    function arbV3(ArbParams memory params) public onlyCaller returns (uint256 profit){
        unchecked {
            if (params.blockNumber != 0) {
                require( block.number <= params.blockNumber, "NB");
            }
            IUniswapV3Pool pool = IUniswapV3Pool(params.pool);
            bool zeroForOne = params.borrowToken < params.repayToken ? false : true;
            uint beforeBalance = IERC20(params.repayToken).balanceOf(profitAddr);
            params.loanSwap = true;
            pool.swap(
                address(this),
                zeroForOne,
                - params.borrowAmount.toInt256(), // 负数表示输出的数量
                zeroForOne ? MIN_SQRT_RATIO + 1 : MAX_SQRT_RATIO - 1,
                abi.encode(params)
            );
            profit = IERC20(params.repayToken).balanceOf(profitAddr) - beforeBalance;
        }
    }

    // called by UniswapV3Pool contract after flash swap
    function uniswapV3SwapCallback(int256 amount0Delta, int256 amount1Delta, bytes calldata data) external {
        ArbParams memory params = abi.decode(data, (ArbParams));
        arbCallBack(amount0Delta, amount1Delta, params);
    }

    function arbCallBack(int256 amount0Delta, int256 amount1Delta, ArbParams memory params) internal {
        unchecked {
            // 如果是第一次flashswap 则特定路径swap
            // 其它情况需要将 token 转移给 pool
            if (params.loanSwap) {
                _swap(params);
            } else {
                // swap 后将 token 转移给 pool
                if (amount0Delta < 0 ) {
                    TransferHelper.safeTransfer(
                        IUniswapV3Pool(msg.sender).token0(), msg.sender, uint256(-amount0Delta)
                    );
                } else if (amount1Delta < 0){
                    TransferHelper.safeTransfer(
                        IUniswapV3Pool(msg.sender).token1(), msg.sender, uint256(-amount1Delta)
                    );
                }
                return;
            }

            // 还款
            // 如果 flash swap 需要将 token 转移给 pool
            TransferHelper.safeTransfer(params.repayToken, msg.sender, params.repayAmount);
            TransferHelper.safeTransfer(
                params.repayToken, profitAddr, IERC20(params.repayToken).balanceOf(address(this))
            );
        }
    }

    function _swap(ArbParams memory params) private {
        // 假设 path = bnb -> cake -> usdt -> bnb
        // 则 pool = (bnb, cake) (cake, usdt) (usdt, bnb)
        unchecked {
            params.loanSwap = false;
            uint256 amountOut = params.borrowAmount;
            while (true) {
                bool isLast = params.pools.length == 1;
                address currentPool;
                (currentPool, params.pools) = popMemoryArray(params.pools);
                address tokenIn;
                (tokenIn, params.path) = popMemoryArray(params.path);
                address tokenOut;
                (tokenOut, params.path) = popMemoryArray(params.path);
                amountOut = exactInputInternal(
                    currentPool,
                    tokenIn,
                    tokenOut,
                    amountOut,
                    params
                );

                if (isLast) {
                    break;
                }
            }
            require(amountOut >= params.repayAmount, 'NP');
        }
    }

    function exactInputInternal(
        address pool,
        address tokenIn,
        address tokenOut,
        uint256 amountIn,
        ArbParams memory data
    ) private returns (uint256 amountOut) {
        bool zeroForOne = tokenIn < tokenOut;

        (int256 amount0, int256 amount1) =
            IUniswapV3Pool(pool).swap(
                address(this),
                zeroForOne,
                amountIn.toInt256(),
                zeroForOne ? MIN_SQRT_RATIO + 1 : MAX_SQRT_RATIO - 1,
                abi.encode(data)
            );

        return uint256(-(zeroForOne ? amount1 : amount0));
    }

    function withdrawToken(address token, address to, uint256 amount) public onlyOwner {
        TransferHelper.safeTransfer(token, to, amount);
    }

    function withdrawETH(address payable _to, uint256 amount) public onlyOwner {
        bool sent = _to.send(amount);
        require(sent, "F");
    }

    function popMemoryArray(address[] memory array) private pure returns (address, address[] memory) {
        address lastElement = array[array.length - 1];
        assembly { mstore(array, sub(mload(array), 1)) }
        return (lastElement, array);
    }

    receive() external payable {}

    fallback() external payable {
        (int256 amount0Delta, int256 amount1Delta,bytes memory data) = abi.decode(msg.data[4 :], (int256, int256, bytes));
        ArbParams memory params = abi.decode(data, (ArbParams));
        arbCallBack(amount0Delta,amount1Delta,params);
    }
}
