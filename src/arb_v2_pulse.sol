// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

//import "forge-std/Test.sol";

import "../src/contract/UniswapV2Router.sol";
import "@uniswap/v3-core/contracts/libraries/LowGasSafeMath.sol";


contract ArbV2 {
    error NotOwner();
    error NoProfit();
    error InvalidPath();
    error InvalidReserve();
    error InvalidTokenA();
    error InvalidTokenB();
    error NotFromPool();
    error InvalidAddress();
    error ZeroAddress();
    error WithdrawFailed();

    using LowGasSafeMath for uint256;
    using LowGasSafeMath for uint8;
    using LowGasSafeMath for uint16;

    address private owner;
    address constant private profitAddr = 0xe4317f0f71597D5f3675fFD505AB887b05bfC610;
    address constant caller1 = 0xf58A24eaA342639f62A2BF7f632362ABc639E4E5;
    address constant caller2 = 0xE35A4eF33104a5B725090752dAdEd78029bA215E;
    address constant caller3 = 0x77E18355BB9AA0A9e165dFEF33de295bC73FD3Da;


    constructor(address _owner) {
        owner = _owner;
    }

    modifier onlyOwner() {
        require(msg.sender == owner, "NO");
        _;
    }

    modifier onlyCaller() {
        if (msg.sender != owner && msg.sender != caller1 && msg.sender != caller2 && msg.sender != caller3){
            revert("NOC");
        }
        _;
    }

    struct ArbParams {
        address pool; // 第一个池子，也是借贷池子, eg: (bnb, cake)
        address repayToken; // 偿还的token
        uint256 repayAmount; // 偿还的数量
        address borrowToken; // 借贷的token
        uint256 borrowAmount; // 借贷的数量
        address[] pools; // 第二个池子到第n个池子 eg: (cake, usdt), (usdt, bnb)
        address[] path; // 第二个池子到第n个池子的路径 eg: cake->usdt->bnb
        uint256 blockNumber; // block.number
    }

    function getAmountsOut(
        uint256 amountIn,
        address[] memory pools,
        address[] memory paths
    ) public view returns (uint256[] memory amounts) {
        unchecked {
            if (paths.length < 2) {
                revert InvalidPath();
            }
            amounts = new uint[](paths.length);
            amounts[0] = amountIn;
            for (uint256 i; i < paths.length - 1; i++) {
                (uint256 reserveIn, uint256 reserveOut) = getReserves(pools[i], paths[i], paths[i + 1]);
                amounts[i + 1] = getAmountOut(amounts[i], reserveIn, reserveOut);
            }
        }
    }

    function getAmountOut(uint256 amountIn, uint256 reserveIn, uint256 reserveOut)
    internal
    pure
    returns (uint256 amountOut)
    {
        unchecked {
            if (reserveIn == 0 || reserveOut == 0) {
                revert InvalidReserve();
            }
            uint256 amountInWithFee = amountIn.mul(997);
            uint256 numerator = amountInWithFee.mul(reserveOut);
            uint256 denominator = reserveIn.mul(1000).add(amountInWithFee);
            amountOut = numerator / denominator;
        }
    }

    function getAmountIn(
        uint256 amountOut,
        uint256 reserveIn,
        uint256 reserveOut
    ) internal pure returns (uint256 amountIn) {
        if (reserveIn < 0 || reserveOut < 0) {
            revert InvalidReserve();
        }
        unchecked {
            uint256 numerator = reserveIn * amountOut * 1000;
            uint256 denominator = (reserveOut - amountOut) * 997;
            amountIn = numerator / denominator + 1;
        }
    }

    function calRepayAmount(
        address pool,
        address borrowToken,
        address repayToken,
        uint256 borrowAmount
    ) internal returns (uint256 repayAmount) {
        unchecked {
            (uint256 reserve0, uint256 reserve1) = getReserves(pool, repayToken, borrowToken);
            repayAmount = getAmountIn(borrowAmount, reserve0, reserve1) + 1;
        }
    }

    function getReserves(address pool, address tokenA, address tokenB)
    public
    view
    returns (uint256 reserveA, uint256 reserveB)
    {
        (uint256 reserve0, uint256 reserve1,) = IUniswapV2Pair(pool).getReserves();
        (address token0,) = sortTokens(tokenA, tokenB);
        (reserveA, reserveB) = tokenA == token0 ? (reserve0, reserve1) : (reserve1, reserve0);
    }

    function arbV2(ArbParams memory params) public onlyCaller returns (uint256 profit){
        // 向第一个池子借贷，完成后续交易后，还款给第一个池子，转移利润
        // 假设 path = bnb -> cake -> usdt -> bnb
        // 则 pool = (bnb, cake) (cake, usdt) (usdt, bnb)
        // 则 amounts = [amount_in, bnb -> cake, cake -> usdt, usdt -> bnb]
        // 实际向第一个池子借 cake，完成swap 路径后，将 bnb 还给第一个池子
        unchecked {
            if (params.blockNumber != 0) {
                require( block.number <= params.blockNumber, "NB");
            }
            // 计算 repayAmount
            params.repayAmount = calRepayAmount(params.pool, params.borrowToken, params.repayToken, params.borrowAmount);
            IUniswapV2Pair pool = IUniswapV2Pair(params.pool);
            (address token0,) = sortTokens(params.borrowToken, params.repayToken);
            (uint256 amount0, uint256 amount1) =
                token0 == params.borrowToken ? (params.borrowAmount, uint256(0)) : (uint256(0), params.borrowAmount);
            uint beforeBalance = IERC20(params.repayToken).balanceOf(profitAddr);
            pool.swap(amount0, amount1, address(this), abi.encode(params));
            profit = IERC20(params.repayToken).balanceOf(profitAddr) - beforeBalance;
        }
    }

    // **** SWAP ****
    // requires the initial amount to have already been sent to the first pair
    // @param path The route of the swap, see Path for details
    function _swap(uint256[] memory amounts, address[] memory pools, address[] memory path, address _to) private {
        // 假设 path = bnb -> cake -> usdt -> bnb
        // 则 pool = (bnb, cake) (cake, usdt) (usdt, bnb)
        // 则 amounts = [amount_in, bnb -> cake, cake -> usdt, usdt -> bnb]
        unchecked {
            for (uint256 i; i < path.length - 1; i++) {
                (address input, address output) = (path[i], path[i + 1]);
                (address token0,) = sortTokens(input, output);
                uint256 amountOut = amounts[i + 1];
                (uint256 amount0Out, uint256 amount1Out) =
                    input == token0 ? (uint256(0), amountOut) : (amountOut, uint256(0));
                address to = i < path.length - 2 ? pools[i + 1] : _to;
                IUniswapV2Pair(pools[i]).swap(amount0Out, amount1Out, to, new bytes(0));
            }
        }
    }

    // called by UniswapV2Pool contract after flash swap
    // @param _sender The UniswapV2Pool contract
    // @param _amount0 flash loan amount of token0
    // @param _amount1 flash loan amount of token1
    // @param data encoded CallbackParams
    function uniswapV2Call(address _sender, uint256 _amount0, uint256 _amount1, bytes calldata data) external {
        ArbParams memory params = abi.decode(data, (ArbParams));
        require(msg.sender == params.pool, "NFP");
        arbCallBack(params);
    }

    function arbCallBack(ArbParams memory params) internal {
        // 第一个池子借贷完成回调
        unchecked {
            uint256[] memory amounts =
                            getAmountsOut(params.borrowAmount, params.pools, params.path);
            require(amounts[amounts.length - 1] > params.repayAmount, "NP");
            // 将借贷的 token 转入第二个池子
            TransferHelper.safeTransfer(params.borrowToken, params.pools[0], params.borrowAmount);
            _swap(amounts, params.pools, params.path, address(this));
            // 还款
            TransferHelper.safeTransfer(params.repayToken, msg.sender, params.repayAmount);
            TransferHelper.safeTransfer(
                params.repayToken, profitAddr, IERC20(params.repayToken).balanceOf(address(this))
            );
        }
    }

    function sortTokens(address tokenA, address tokenB) internal pure returns (address token0, address token1) {
        if (tokenA == tokenB) {
            revert InvalidAddress();
        }
        (token0, token1) = tokenA < tokenB ? (tokenA, tokenB) : (tokenB, tokenA);
        if (token0 == address(0)) {
            revert ZeroAddress();
        }
    }

    function withdrawToken(address token, address to, uint256 amount) public onlyOwner {
        TransferHelper.safeTransfer(token, to, amount);
    }

    function withdrawETH(address payable _to, uint256 amount) public onlyOwner {
        bool sent = _to.send(amount);
        if (!sent) {
            revert WithdrawFailed();
        }
    }

    receive() external payable {}

    fallback() external payable {
        (,,,bytes memory data) = abi.decode(msg.data[4 :], (address, uint256, uint256, bytes));
        ArbParams memory params = abi.decode(data, (ArbParams));
        arbCallBack(params);
    }
}
