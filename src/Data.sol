// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.0;


interface IUniswapV3Pool {
    function token0() external view returns (address);

    function token1() external view returns (address);

    function fee() external view returns (uint24);

    function tickSpacing() external view returns (int24);

    function liquidity() external view returns (uint128);

    function slot0()
    external
    view
    returns (
        uint160 sqrtPriceX96,
        int24 tick,
        uint16 observationIndex,
        uint16 observationCardinality,
        uint16 observationCardinalityNext,
        uint8 feeProtocol,
        bool unlocked
    );

    function ticks(int24 tick)
    external
    view
    returns (
        uint128 liquidityGross,
        int128 liquidityNet,
        uint256 feeGrowthOutside0X128,
        uint256 feeGrowthOutside1X128,
        int56 tickCumulativeOutside,
        uint160 secondsPerLiquidityOutsideX128,
        uint32 secondsOutside,
        bool initialized
    );
}

contract Data {
    struct PoolData {
        address pool;
        address tokenA;
        string tokenASymbol;
        uint8 tokenADecimals;
        address tokenB;
        string tokenBSymbol;
        uint8 tokenBDecimals;
        uint128 liquidity;
        uint160 sqrtPrice;
        int24 tick;
        int24 tickSpacing;
        uint24 fee;
        int128 liquidityNet;
    }
    constructor() {
    }

    function getPoolData(address[] memory pools) public returns (PoolData[] memory poolDatas) {
        PoolData[] memory allPoolData = new PoolData[](pools.length);

        for (uint256 i = 0; i < pools.length; ++i) {
            address poolAddress = pools[i];

            if (codeSizeIsZero(poolAddress)) continue;

            PoolData memory poolData;

            poolData.tokenA = IUniswapV3Pool(poolAddress).token0();
            poolData.tokenB = IUniswapV3Pool(poolAddress).token1();

            //Check that tokenA and tokenB do not have codesize of 0
            if (codeSizeIsZero(poolData.tokenA)) continue;
            if (codeSizeIsZero(poolData.tokenB)) continue;

            //Get tokenA decimals
            (
                bool tokenADecimalsSuccess,
                bytes memory tokenADecimalsData
            ) = poolData.tokenA.call(abi.encodeWithSignature("decimals()"));

            if (tokenADecimalsSuccess) {
                uint256 tokenADecimals;

                if (tokenADecimalsData.length == 32) {
                    (tokenADecimals) = abi.decode(
                        tokenADecimalsData,
                        (uint256)
                    );

                    if (tokenADecimals == 0 || tokenADecimals > 255) {
                        continue;
                    } else {
                        poolData.tokenADecimals = uint8(tokenADecimals);
                    }
                } else {
                    continue;
                }
            } else {
                continue;
            }

            //Get tokenA symbol
            (
                bool tokenASymbolsSuccess,
                bytes memory tokenASymbolData
            ) = poolData.tokenA.call(abi.encodeWithSignature("symbol()"));
            if (tokenASymbolsSuccess) {
                (poolData.tokenASymbol) = abi.decode(
                    tokenASymbolData,
                    (string)
                );
            }

            (
                bool tokenBDecimalsSuccess,
                bytes memory tokenBDecimalsData
            ) = poolData.tokenB.call(abi.encodeWithSignature("decimals()"));

            if (tokenBDecimalsSuccess) {
                uint256 tokenBDecimals;
                if (tokenBDecimalsData.length == 32) {
                    (tokenBDecimals) = abi.decode(
                        tokenBDecimalsData,
                        (uint256)
                    );

                    if (tokenBDecimals == 0 || tokenBDecimals > 255) {
                        continue;
                    } else {
                        poolData.tokenBDecimals = uint8(tokenBDecimals);
                    }
                } else {
                    continue;
                }
            } else {
                continue;
            }

            //Get tokenB symbol
            (
                bool tokenBSymbolsSuccess,
                bytes memory tokenBSymbolData
            ) = poolData.tokenA.call(abi.encodeWithSignature("symbol()"));
            if (tokenBSymbolsSuccess) {
                (poolData.tokenBSymbol) = abi.decode(
                    tokenBSymbolData,
                    (string)
                );
            }

            (uint160 sqrtPriceX96, int24 tick, , , , ,) = IUniswapV3Pool(
                poolAddress
            ).slot0();

            (, int128 liquidityNet, , , , , ,) = IUniswapV3Pool(poolAddress)
                .ticks(tick);

            poolData.pool = poolAddress;
            poolData.liquidity = IUniswapV3Pool(poolAddress).liquidity();
            poolData.tickSpacing = IUniswapV3Pool(poolAddress).tickSpacing();
            poolData.fee = IUniswapV3Pool(poolAddress).fee();

            poolData.sqrtPrice = sqrtPriceX96;
            poolData.tick = tick;

            poolData.liquidityNet = liquidityNet;

            allPoolData[i] = poolData;
        }
        return poolDatas;
    }

    function codeSizeIsZero(address target) internal view returns (bool) {
        if (target.code.length == 0) {
            return true;
        } else {
            return false;
        }
    }

    struct TickData {
        bool initialized;
        int24 tick;
        int128 liquidityNet;
    }

    function getTickData() public pure returns (TickData[] memory tickDatas) {
        tickDatas = new TickData[](2);
        tickDatas[0] = TickData({
            initialized: true,
            tick: 0,
            liquidityNet: 100000
        });
        tickDatas[1] = TickData({
            initialized: true,
            tick: 0,
            liquidityNet: 100001
        });
        return tickDatas;
    }

    struct V2PoolData {
        address pool;
        address tokenA;
        uint8 tokenADecimals;
        string tokenASymbol;
        address tokenB;
        uint8 tokenBDecimals;
        string tokenBSymbol;
        uint112 reserve0;
        uint112 reserve1;
    }

    function getV2Pairs() public returns (address[] memory pairs) {
        pairs = new address[](1);
        pairs[1] = address(this);
        return pairs;
    }

    function getV2PoolData() public returns (V2PoolData[] memory poolDatas) {
        poolDatas = new V2PoolData[](1);
        poolDatas[0] = V2PoolData({
            pool: address(this),
            tokenA: address(this),
            tokenADecimals: 18,
            tokenASymbol: "ETH",
            tokenB: address(this),
            tokenBDecimals: 18,
            tokenBSymbol: "ETH",
            reserve0: 100000,
            reserve1: 100000
        });
        return poolDatas;
    }
}