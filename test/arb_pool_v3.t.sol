// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import "../src/arb_pool_v3.sol";



contract ArbPoolV3Test is Test {
    ArbV3 public arb;
    address public profitAddr = ******************************************;


    function setUp() public {
        arb = new ArbV3(address(this));
    }

    function testSendETHToArbContract() public {
        deal(address(this), 1 ether);
        // send eth to contract
        address payable arbAddr = payable(address(arb));
        arbAddr.transfer(1 ether);
        // check arb eth balance
        assertEq(arbAddr.balance, 1 ether);
        assertEq(address(this).balance, 0);

        address payable thisAddr = payable(******************************************);
        arb.withdrawETH(thisAddr, 0.9 ether);
        assertEq(arbAddr.balance, 0.1 ether, "arb addr balance");
        assertEq(thisAddr.balance, 0.9 ether, "this balance");
    }

    function testSendTokenToContract() public {
        address token = ******************************************;
        deal(token, address(arb), 1e18);
        assertEq(IERC20(token).balanceOf(address(arb)), 1e18, "init token failed");

        arb.withdrawToken(token, address(this), 1e18);

        assertEq(IERC20(token).balanceOf(address(arb)), 0, "arb balance not 0");
        assertEq(IERC20(token).balanceOf(address(this)), 1e18, "this balance not right");
    }

    function testPulseOnlineArb() public {
        address token = ******************************************;

        // ArbV3.ArbParams memory params = ArbV3.ArbParams({
        //     pool: poolA,
        //     borrowToken: path[0],
        //     borrowAmount: borrowAmount,
        //     repayToken: token,
        //     repayAmount: repayAmount+1,
        //     pools: pools,
        //     path: path,
        //     blockNumber: block.number + 1,
        //     loanSwap: false
        // });
        // arb.arbV3(params);
    }
}
