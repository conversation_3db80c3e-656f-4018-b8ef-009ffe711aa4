// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import "../src/ArbV4.sol";

contract ArbV4Test is Test {
    ArbV4 public arb;
    address RIF = ******************************************;
    address WBTC = ******************************************;

    function setUp() public {
        arb = new ArbV4(address(this));
    }

    function testSendETHToArbContract() public {
        deal(address(this), 1 ether);
        // send eth to contract
        address payable arbAddr = payable(address(arb));
        arbAddr.transfer(1 ether);
        // check arb eth balance
        assertEq(arbAddr.balance, 1 ether);
        assertEq(address(this).balance, 0);

        address payable thisAddr = payable(******************************************);
        arb.withdrawETH(thisAddr, 0.9 ether);
        assertEq(arbAddr.balance, 0.1 ether, "arb addr balance");
        assertEq(thisAddr.balance, 0.9 ether, "this balance");
    }

    function testSendTokenToContract() public {
        address token = ******************************************;
        deal(token, address(arb), 1e18);
        assertEq(IERC20(token).balanceOf(address(arb)), 1e18, "init token failed");

        arb.withdrawToken(token, address(this), 1e18);

        assertEq(IERC20(token).balanceOf(address(arb)), 0, "arb balance not 0");
        assertEq(IERC20(token).balanceOf(address(this)), 1e18, "this balance not right");
    }

    function testStartArb1() public {
        // uniswap v3  RIF/WBTC
        // sovryn RIF/WBTC
        // sovryn 中的 RIF 价格高于 uniswap v3 中的 RIF 价格
        // uniswap v3 flash swap -> RIF -> sovryn swap -> WBTC -> uniswap v3 repay WBTC

        address loanPool = ******************************************;
        address repayToken = ******************************************; // WBTC
        address borrowToken = ******************************************; // RIF
        address[] memory arbPath = new address[](3);
        arbPath[0] = ******************************************;
        arbPath[1] = ******************************************;
        arbPath[2] = ******************************************;
        uint256 borrowAmount = 6109705112699338404876;
        uint256 repayAmount = 18271079999999471;
        ArbV4.ArbParams memory params = ArbV4.ArbParams({
            loanPool: loanPool,
            arbPath: arbPath,
            repayToken: repayToken,
            repayAmount: repayAmount,
            borrowToken: borrowToken,
            borrowAmount: borrowAmount,
            swapAmount: borrowAmount,
            profitToken: ******************************************
        });
        arb.startArb(params);
    }

    function testStartArb2() public {
        // 在 sovryn 中 买入 WBTC 使得 uniswap 中的 RIF/WBTC 价格高于 sovryn 中的 RIF/WBTC 价格
        uint256 buyAmount = 100000e18;
        address RIFWBTCAnchor = ******************************************;
        deal(******************************************, address(this), buyAmount);
        address[] memory path = new address[](3);
        path[0] = RIF;
        path[1] = RIFWBTCAnchor;
        path[2] = WBTC;
        IERC20(RIF).approve(******************************************, buyAmount);

        ISovrynPool RIFWBTCPool = ISovrynPool(******************************************);
        uint256 rifBalance = RIFWBTCPool.reserveBalance(RIF);
        uint32 rifWeight = RIFWBTCPool.reserveWeight(RIF);
        uint256 wbtcBalance = RIFWBTCPool.reserveBalance(WBTC);
        uint32 wbtcWeight = RIFWBTCPool.reserveWeight(WBTC);
        console2.log("rif balance: ", rifBalance);
        console2.log("rif weight: ", rifWeight);
        console2.log("wbtc balance: ", wbtcBalance);
        console2.log("wbtc weight: ", wbtcWeight);

        ISovrynSwap(******************************************).convertByPath(
            path, buyAmount, 1, address(this), address(0), 0
        );

        console2.log("start test arb2");
        // uniswap 中的 RIF/WBTC 价格高于 sovryn 中的 RIF/WBTC 价格
        // uniswap 中的 WBTC/RIF 价格低于 sovryn 中的 WBTC/RIF 价格
        // 相同数量的 BTC 可以在 sovryn 中换得更多的 RIF
        // 1. 在 uniswap 中 flash swap WBTC
        // 2. 将 WBTC 的一部分在 sovryn 中 swap 成 RIF
        // 3. 将 RIF 还给 uniswap v3
        // 4. 将利润转给 profitAddr
        address loanPool = ******************************************;
        address repayToken = RIF; // RIF
        address borrowToken = WBTC; // WBTC
        address[] memory arbPath = new address[](3);

        arbPath[0] = WBTC;
        arbPath[1] = RIFWBTCAnchor;
        arbPath[2] = RIF;
        uint256 borrowAmount = 18271079999999471;
        uint256 repayAmount = 6131910603779415970416;
        uint256 swapAmount = 17593341517235756;

        uint256 swapAmountCal = ISovrynSwap(******************************************).rateByPath(path, repayAmount);
        console2.log("swapAmount: ", swapAmount);
        console2.log("swapAmount cal: ", swapAmountCal);

        ArbV4.ArbParams memory params = ArbV4.ArbParams({
            loanPool: loanPool,
            arbPath: arbPath,
            repayToken: repayToken,
            repayAmount: repayAmount,
            borrowToken: borrowToken,
            borrowAmount: borrowAmount,
            swapAmount: swapAmount,
            profitToken: ******************************************
        });
        arb.startArb(params);
    }
}
