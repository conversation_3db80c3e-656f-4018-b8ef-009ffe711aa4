// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../src/Data.sol";


contract DataBscTest is Test {
    Data public data;

    function setUp() public {
        data = new Data();
    }

    function testGetPoolData() public {
        address[] memory pools = new address[](1);
        pools[0] = 0x36696169C63e42cd08ce11f5deeBbCeBae652050;
        Data.PoolData[] memory result = data.getPoolData(pools);
        console2.log("tokenA:", result[0].tokenA);
        console2.log("tokenA decimals:", result[0].tokenADecimals);
    }
}