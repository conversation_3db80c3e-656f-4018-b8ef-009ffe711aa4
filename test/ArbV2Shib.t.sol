// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "forge-std/Test.sol";

interface Withdrawable {
    function withdraw(uint256 wad) external;
}

interface IERC20 {
    function balanceOf(address account) external view returns (uint256);
}

contract ArbV2ShibTest is Test {
    address constant caller1 = 0xE35A4eF33104a5B725090752dAdEd78029bA215E;
    address constant caller2 = 0x77E18355BB9AA0A9e165dFEF33de295bC73FD3Da;
    address constant caller3 = 0xf58A24eaA342639f62A2BF7f632362ABc639E4E5;

    function testWithdrawToken() public {
        address[] memory wrapTokens = new address[](7);
        wrapTokens[0] = 0xE403B78b14de6D50fa1674c0Ca6331753bD87bA4;
        wrapTokens[1] = 0x1b2F364032f12BD8a4c89E672e6272De03ae2680;
        wrapTokens[2] = 0x62AEF37A8C20E177Bc4dA6AFf6146275E97f349e;
        wrapTokens[3] = 0xa2899c776bAAF9925d432F83C950D5054A6CF59C;
        wrapTokens[4] = 0x6c19A35875217b134e963ca9e61b005b855CAD21;
        wrapTokens[5] = 0x052C88faBc090b40843689c9a6888957868B5C85;
        wrapTokens[6] = 0x26cB8660EeFCB2F7652e7796ed713c9fB8373f8e;

        console2.log("token length: ", wrapTokens.length);
        for (uint256 i = 0; i < wrapTokens.length; i++) {
            deal(caller1, 1e18);
            deal(caller2, 1e18);
            deal(caller3, 1e18);

            deal(wrapTokens[i], caller1, 1e18);
            deal(wrapTokens[i], caller2, 2e18);
            deal(wrapTokens[i], caller3, 3e18);
            assertEq(IERC20(wrapTokens[i]).balanceOf(caller1), 1e18, "caller1 init token failed");
            assertEq(IERC20(wrapTokens[i]).balanceOf(caller2), 2e18, "caller2 init token failed");
            assertEq(IERC20(wrapTokens[i]).balanceOf(caller3), 3e18, "caller3 init token failed");

            vm.startPrank(caller1);
            (bool success,) = wrapTokens[i].call(abi.encodeWithSignature("withdraw(uint256)", 1e18));
            if (!success) {
                console2.log("withdraw failed");
            }
            assertEq(IERC20(wrapTokens[i]).balanceOf(caller1), 0, "caller1 balance not 0");
            assertEq(caller1.balance, 2e18, "caller1 balance not right");
            vm.stopPrank();

            vm.startPrank(caller2);
            (success,) = wrapTokens[i].call(abi.encodeWithSignature("withdraw(uint256)", 1e18));
            if (!success) {
                console2.log("withdraw failed");
            }
            assertEq(IERC20(wrapTokens[i]).balanceOf(caller2), 1e18, "caller2 balance not 0");
            assertEq(caller2.balance, 2e18, "caller2 balance not right");
            vm.stopPrank();

            vm.startPrank(caller3);
            (success,) = wrapTokens[i].call(abi.encodeWithSignature("withdraw(uint256)", 1e18));
            if (!success) {
                console2.log("withdraw failed");
            }
            assertEq(IERC20(wrapTokens[i]).balanceOf(caller3), 2e18, "caller3 balance not equal");
            assertEq(caller3.balance, 2e18, "caller3 balance not right");
            vm.stopPrank();
        }
    }
}
