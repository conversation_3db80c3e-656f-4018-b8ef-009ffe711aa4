// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import "../src/arb_v2_pulse.sol";

import "../src/contract/UniswapV2Router.sol";


contract ArbV2PulseTest is Test {
    ArbV2 public arb;
    address public wpls = ******************************************;
    address public ATROPA = ******************************************;
    address public thex = ******************************************;
    address public profitAddr = ******************************************;


    function setUp() public {
        arb = new ArbV2(address(this));
    }

    function testSendETHToArbContract() public {
        deal(address(this), 1 ether);
        // send eth to contract
        address payable arbAddr = payable(address(arb));
        arbAddr.transfer(1 ether);
        // check arb eth balance
        assertEq(arbAddr.balance, 1 ether);
        assertEq(address(this).balance, 0);

        address payable thisAddr = payable(******************************************);
        arb.withdrawETH(thisAddr, 0.9 ether);
        assertEq(arbAddr.balance, 0.1 ether, "arb addr balance");
        assertEq(thisAddr.balance, 0.9 ether, "this balance");
    }

    function testSendTokenToContract() public {
        deal(wpls, address(arb), 1e18);
        assertEq(IERC20(wpls).balanceOf(address(arb)), 1e18, "init token failed");

        arb.withdrawToken(wpls, address(this), 1e18);

        assertEq(IERC20(wpls).balanceOf(address(arb)), 0, "arb balance not 0");
        assertEq(IERC20(wpls).balanceOf(address(this)), 1e18, "this balance not right");
    }

    function testPulseOnlineArb() public {
        address poolA = ******************************************;
        address poolB = ******************************************;
        address poolC = ******************************************;
        address[] memory pools = new address[](2);
        pools[0] = poolB;
        pools[1] = poolC;
        address[] memory path = new address[](3);
        path[0] = ******************************************;
        path[1] = ******************************************;
        path[2] = wpls;
        uint borrowAmount = 3225051399974145734491;
        uint repayAmount = 0;
        ArbV2.ArbParams memory params = ArbV2.ArbParams({
            pool: poolA,
            borrowToken: path[0],
            borrowAmount: borrowAmount,
            repayToken: wpls,
            repayAmount: repayAmount+1,
            pools: pools,
            path: path,
            blockNumber: block.number + 1
        });
        arb.arbV2(params);
        uint256 profit = IERC20(params.repayToken).balanceOf(address(profitAddr));
        console2.log("profit: ", profit);
        assertGt(profit, 0, "profit should gt 0");
    }
}
