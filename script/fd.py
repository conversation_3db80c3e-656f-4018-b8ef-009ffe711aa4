from sympy import diff, solve, symbols, simplify, init_printing, latex
from sympy.abc import x
from sympy.plotting import plot

BigInt = int

init_printing()


def gen_get_amount_out(reserve_in: BigInt, reserve_out: BigInt):
    def f(amount_in: BigInt):
        amount_in_with_fee = amount_in * 997
        numerator = amount_in_with_fee * reserve_out
        denominator = reserve_in * 1000 + amount_in_with_fee
        amount_out = numerator / denominator
        return amount_out

    return f


def gen_cal_profit(reserve_list):
    def cal_profit(amount_in):
        amount_out = amount_in
        for reserve_in, reserve_out in reserve_list:
            f = gen_get_amount_out(reserve_in, reserve_out)
            amount_out = f(amount_out)
        return amount_out - amount_in

    return cal_profit


def plot_k(reserve_list):
    # (x + delta_x) * (y - delta_y) = xy
    # delta_y = y - xy / (x + delta_x)
    p_k = plot(xlim=(0, 2), ylim=(0, 2))
    for reserve_in, reserve_out in reserve_list:
        f = reserve_in * reserve_out / x
        p = plot(f, show=False, axis_center=(0, 0))
        p_k.extend(p)
    p_k.show()


def plot_amount_in(reserve_list):
    # (x + delta_x) * (y - delta_y) = xy
    # delta_y = y - xy / (x + delta_x)
    p_amount_in = plot()
    for reserve_in, reserve_out in reserve_list:
        f = reserve_out - reserve_in * reserve_out / (x + reserve_in)
        p = plot(f, show=False, axis_center=(0, 0))
        p_amount_in.extend(p)
    p_amount_in.show()


def cal_amount_in_to_max_profit(reserve_list, with_plot=False):
    cal_profit = gen_cal_profit(reserve_list)
    cal_profit_f = diff(cal_profit(x), x)  # 利润函数的导数
    if with_plot:
        # p1 = plot(cal_profit_f, show=False, axis_center=(0, 0), label="profit_diff", xlim=(-10000, 100000))
        p2 = plot(cal_profit(x), show=False, axis_center=(0, 0), label="profit", xlim=(-10000, 100000))
        # p1.extend(p2)
        # p1.show()
        p2.show()
    result = solve(cal_profit_f, x)
    amount_in = int(result[0])
    profit = cal_profit(amount_in)
    return amount_in, profit


def test_cal():
    # f1 = gen_get_amount_out(Decimal(1030), Decimal(2000))
    # f2 = gen_get_amount_out(Decimal(2010), Decimal(1888))
    # f3 = gen_get_amount_out(Decimal(2111), Decimal(1543))
    # reserve_list = [
    #     [Decimal(2111), Decimal(1543)],
    #     [Decimal(2010), Decimal(1888)],
    #     [Decimal(1030), Decimal(2000)],
    # ]
    # 假设套路路径 bnb -> cake -> usdt -> bnb
    reserve_list = [
        [BigInt(50 * 10 ** 18), BigInt(6685 * 10 ** 18)],  # bnb/cake
        [BigInt(20000 * 10 ** 18), BigInt(32880 * 10 ** 18)],  # cake/usdt
        [BigInt(24300 * 10 ** 18), BigInt(115 * 10 ** 18)],  # usdt/bnb
    ]
    # plot_k(reserve_list)
    print("max_profit", cal_amount_in_to_max_profit(reserve_list))


def cal_amount_in1(amount_in, reserve_in, reserve_out, fee_rate_denominator, fee):
    pass


def cal_amount_out1(amount_in: any, reserve_in, reserve_out, fee_rate_denominator=10000, fee=30):
    # amount_in_with_fee = amount_in * 997
    amount_in_with_fee = amount_in * (fee_rate_denominator - fee)
    numerator = amount_in_with_fee * reserve_out
    # denominator = reserve_in * 1000 + amount_in_with_fee
    denominator = reserve_in * fee_rate_denominator + amount_in_with_fee
    amount_out = numerator / denominator
    profit = amount_out - amount_in
    return amount_out, profit


def cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2):
    amount_in_2, _ = cal_amount_out1(amount_in, reserve_in_1, reserve_out_1)
    cal_amount_out_2, _ = cal_amount_out1(amount_in_2, reserve_in_2, reserve_out_2)
    cal_profit_2 = cal_amount_out_2 - amount_in
    return cal_amount_out_2, cal_profit_2


def cal_amount_out3(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3):
    amount_in_2, _ = cal_amount_out1(amount_in, reserve_in_1, reserve_out_1)
    cal_amount_out_2, _ = cal_amount_out1(amount_in_2, reserve_in_2, reserve_out_2)
    cal_amount_out_3, _ = cal_amount_out1(cal_amount_out_2, reserve_in_3, reserve_out_3)
    cal_profit = cal_amount_out_3 - amount_in
    return cal_amount_out_3, cal_profit


def cal_amount_in_to_max_profit_fd_1(amount_in, reserve_in, reserve_out):
    cal_amount_out, cal_profit = cal_amount_out1(amount_in, reserve_in, reserve_out)
    cal_profit_diff = diff(cal_profit, amount_in)
    return cal_profit_diff


def cal_amount_in_to_max_profit_fd_2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2):
    cal_amount_out, cal_profit = cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)
    cal_profit_diff = diff(cal_profit, amount_in)
    return cal_profit_diff


def get_cal_amount_in_to_max_profit_fd_2():
    amount_in = symbols('amount_in')
    reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2 = symbols(
        'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2')
    cal_amount_out, cal_profit = cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)
    cal_profit_diff = diff(cal_profit, amount_in)

    def f(amount_in_arg, reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg):
        return cal_profit_diff.subs({
            amount_in: amount_in_arg,
            reserve_in_1: reserve_in_1_arg,
            reserve_out_1: reserve_out_1_arg,
            reserve_in_2: reserve_in_2_arg,
            reserve_out_2: reserve_out_2_arg,
        })

    def cal_profit_func(amount_in_arg, reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg):
        return cal_profit.subs({
            amount_in: amount_in_arg,
            reserve_in_1: reserve_in_1_arg,
            reserve_out_1: reserve_out_1_arg,
            reserve_in_2: reserve_in_2_arg,
            reserve_out_2: reserve_out_2_arg,
        })

    return f, cal_profit_func


def get_cal_amount_in_to_max_profit_fd_inverse_2():
    amount_in = symbols('amount_in')
    reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2 = symbols(
        'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2')
    cal_amount_out, cal_profit = cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)
    # 计算 profit 斜率 随 amount_in 的变化，当 profit 斜率为0 时，profit 最大，计算出此时 amount_in 的值
    cal_profit_diff = diff(cal_profit, amount_in)
    result = solve(cal_profit_diff, amount_in)
    if result:
        func = result[1]
        print("func: \n", func)
        print("cal_profit: \n", cal_profit)

        def cal_max_profit_func(reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg):
            return func.subs({
                reserve_in_1: reserve_in_1_arg,
                reserve_out_1: reserve_out_1_arg,
                reserve_in_2: reserve_in_2_arg,
                reserve_out_2: reserve_out_2_arg,
            })

        def cal_profit_func(amount_in_arg, reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg):
            return cal_profit.subs({
                amount_in: amount_in_arg,
                reserve_in_1: reserve_in_1_arg,
                reserve_out_1: reserve_out_1_arg,
                reserve_in_2: reserve_in_2_arg,
                reserve_out_2: reserve_out_2_arg,
            })

        return cal_max_profit_func, cal_profit_func


def get_cal_amount_in_to_max_profit_fd_inverse_3():
    amount_in = symbols('amount_in')
    reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3 = symbols(
        'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2 reserve_in_3 reserve_out_3')
    cal_amount_out, cal_profit = cal_amount_out3(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2,
                                                 reserve_in_3, reserve_out_3)
    # 计算 profit 斜率 随 amount_in 的变化，当 profit 斜率为0 时，profit 最大，计算出此时 amount_in 的值
    cal_profit_diff = diff(cal_profit, amount_in)
    cal_profit_diff = simplify(cal_profit_diff)
    result = solve(cal_profit_diff, amount_in)
    if result:
        func = result[1]
        print(f"func1: \n{latex(result[0])}")
        print(f"func2: \n{latex(result[1])}")
        print(f"result[0]:\n {result[0]}\n")
        print(f"result[1]:\n {result[1]}\n")
        print("cal_profit: \n", cal_profit)

        def cal_max_profit_func(reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg,
                                reserve_in_3_arg, reserve_out_3_arg):
            return func.subs({
                reserve_in_1: reserve_in_1_arg,
                reserve_out_1: reserve_out_1_arg,
                reserve_in_2: reserve_in_2_arg,
                reserve_out_2: reserve_out_2_arg,
                reserve_in_3: reserve_in_3_arg,
                reserve_out_3: reserve_out_3_arg
            })

        def cal_profit_func(amount_in_arg, reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg,
                            reserve_in_3_arg, reserve_out_3_arg):
            return cal_profit.subs({
                amount_in: amount_in_arg,
                reserve_in_1: reserve_in_1_arg,
                reserve_out_1: reserve_out_1_arg,
                reserve_in_2: reserve_in_2_arg,
                reserve_out_2: reserve_out_2_arg,
                reserve_in_3: reserve_in_3_arg,
                reserve_out_3: reserve_out_3_arg
            })

        return cal_max_profit_func, cal_profit_func


def test_max_profit_2():
    amount_in = 2.3 * 10 ** 18
    # 套利路径 bnb -> cake -> bnb
    reserve_in_1 = 50 * 10 ** 18
    reserve_out_1 = 6685 * 10 ** 18
    reserve_in_2 = 20000 * 10 ** 18
    reserve_out_2 = 169 * 10 ** 18
    f, cal_profit = get_cal_amount_in_to_max_profit_fd_inverse_2()
    if not f:
        return
    max_amount_in = f(reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)
    max_profit = cal_profit(max_amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)
    print(f"max_amount_in: {float(max_amount_in)}\nmax_profit: {float(max_profit)}")
    profit = cal_profit(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)
    print(f"amount_in: {amount_in}\nprofit: {float(profit)}")
    plot(cal_profit(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2),
         (amount_in, 0, 100000000000000000000))


def test_max_profit_3():
    amount_in = 2.3 * 10 ** 18
    # 套利路径 bnb -> cake -> usdt -> bnb
    reserve_in_1 = 50 * 10 ** 18
    reserve_out_1 = 6685 * 10 ** 18
    reserve_in_2 = 3500 * 10 ** 18
    reserve_out_2 = 6800 * 10 ** 18
    reserve_in_3 = 253433 * 10 ** 18
    reserve_out_3 = 1034 * 10 ** 18
    f, cal_profit = get_cal_amount_in_to_max_profit_fd_inverse_3()
    if not f:
        return
    max_amount_in = f(reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3)
    max_profit = cal_profit(max_amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3,
                            reserve_out_3)
    print(f"max_amount_in: {float(max_amount_in)}\nmax_profit: {float(max_profit)}")
    profit = cal_profit(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3,
                        reserve_out_3)
    print(f"amount_in: {amount_in}\nprofit: {float(profit)}")
    amount_in = symbols("amount_in")
    plot(cal_profit(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3),
         (amount_in, 0, 100000000000000000000))


def test_fd2():
    amount_in = 1 * 10 ** 18
    # 套利路径 bnb -> cake -> bnb
    reserve_in_1 = 50 * 10 ** 18
    reserve_out_1 = 6685 * 10 ** 18
    reserve_in_2 = 20000 * 10 ** 18
    reserve_out_2 = 169 * 10 ** 18
    f, cal_profit_func = get_cal_amount_in_to_max_profit_fd_2()
    ratio = f(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)
    print("ratio: \n", ratio)
    print("profit: \n", cal_profit_func(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2))


def cal_profit_plot_2():
    amount_in = symbols('amount_in')

    # reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2 = symbols(
    #     'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2')
    reserve_in_1 = 50 * 10 ** 18
    reserve_out_1 = 6685 * 10 ** 18
    reserve_in_2 = 20000 * 10 ** 18
    reserve_out_2 = 169 * 10 ** 18

    # reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2 = symbols(
    #     'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2')
    cal_amount_out, cal_profit = cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)
    print("start profit plot")
    print(cal_profit)
    p1 = plot(cal_amount_out, (amount_in, 0, 160 * 10 ** 18), ylim=(0, 50 * 10 ** 18), show=False, ylabel="amount_out")
    p2 = plot(amount_in, (amount_in, 0, 160 * 10 ** 18), show=False)
    p1.extend(p2)
    p1.show()
    plot(cal_profit, (amount_in, 0, 10 ** 19), ylabel="profit")
    plot(diff(cal_profit, amount_in), (amount_in, -10 ** 20, 10 ** 19), ylabel="profit_diff")
    print("end profit plot")


def cal_profit_plot_3():
    amount_in = symbols('amount_in')

    # bnb -> cake -> usdt -> bnb
    reserve_in_1 = 50 * 10 ** 18
    reserve_out_1 = 6685 * 10 ** 18
    reserve_in_2 = 3500 * 10 ** 18
    reserve_out_2 = 6800 * 10 ** 18
    reserve_in_3 = 253433 * 10 ** 18
    reserve_out_3 = 1034 * 10 ** 18

    cal_amount_out, cal_profit = cal_amount_out3(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2,
                                                 reserve_in_3, reserve_out_3)
    print("start profit plot")
    print(cal_profit)
    p1 = plot(cal_amount_out, (amount_in, 0, 160 * 10 ** 18), ylim=(0, 50 * 10 ** 18), show=False, ylabel="amount_out")
    p2 = plot(amount_in, (amount_in, 0, 160 * 10 ** 18), show=False)
    p1.extend(p2)
    p1.show()
    plot(cal_profit, (amount_in, 0, 10 ** 19), ylabel="profit")
    plot(diff(cal_profit, amount_in), (amount_in, -10 ** 20, 10 ** 19), ylabel="profit_diff")
    print("end profit plot")


def main():
    amount_in = symbols('amount_in')
    reserve_in, reserve_out = symbols('reserve_in reserve_out')
    # (x + delta_x) * (y - delta_y) = xy
    # delta_y = y - xy / (x + delta_x)
    # result = cal_amount_out1(amount_in, reserve_in, reserve_out)
    # print(result[0])
    # print(result[1])
    # reserve_in_1, reserve_out_1 = symbols('reserve_in_1 reserve_out_1')
    # reserve_in_2, reserve_out_2 = symbols('reserve_in_2 reserve_out_2')
    # result = cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)
    # print(result[0])
    # print(result[1])
    # cal_profit_2 = result[1]
    # print("cal_profit_2: \n", cal_profit_2)
    # result = cal_amount_in_to_max_profit_fd_1(amount_in, reserve_in, reserve_out)
    # print(result)
    # result = cal_amount_in_to_max_profit_fd_2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)
    # print("origin:\n", result)
    # print("expand:\n", expand(result))
    # print("simplify:\n", simplify(result))
    # print("cancel:\n", cancel(result))
    # test_fd2()
    # result = get_cal_amount_in_to_max_profit_fd_inverse_2()
    # print("origin:\n", result[0])
    # print("expand:\n", expand(result))
    # print("simplify:\n", simplify(result))
    # print("cancel:\n", cancel(result))
    test_max_profit_3()
    # cal_profit_plot()


if __name__ == '__main__':
    main()
