import datetime
import typing


def convert(raw_str: str):
    items = raw_str.split()
    column_len = 17
    result = []
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    for i in range(0, len(items), column_len):
        result.append(','.join(items[i:i + column_len - 1] + [now, now]))
    return result

def get_has_same_pair(pairs: typing.List, tokens: typing.List[str]) -> typing.List:
    '''
    获取 pairs 中存在只由 tokens 组成的 pair
    '''
    tokens = [token.lower() for token in tokens]
    result = []
    for pair in pairs:
        if all([pair["Token0"].lower() in tokens, pair["Token1"].lower() in tokens]):
            result.append(pair)
    return result