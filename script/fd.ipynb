{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ed4e9919", "metadata": {"ExecuteTime": {"end_time": "2023-08-15T14:26:34.806353Z", "start_time": "2023-08-15T14:26:34.803716Z"}}, "outputs": [], "source": ["from sympy import diff, solve, symbols, simplify\n", "from sympy.abc import x\n", "from sympy.plotting import plot\n", "\n", "BigInt = int\n", "\n", "\n", "def gen_get_amount_out(reserve_in: BigInt, reserve_out: BigInt):\n", "    def f(amount_in: BigInt):\n", "        amount_in_with_fee = amount_in * 997\n", "        numerator = amount_in_with_fee * reserve_out\n", "        denominator = reserve_in * 1000 + amount_in_with_fee\n", "        amount_out = numerator / denominator\n", "        return amount_out\n", "\n", "    return f\n", "\n", "\n", "def gen_cal_profit(reserve_list):\n", "    def cal_profit(amount_in):\n", "        amount_out = amount_in\n", "        for reserve_in, reserve_out in reserve_list:\n", "            f = gen_get_amount_out(reserve_in, reserve_out)\n", "            amount_out = f(amount_out)\n", "        return amount_out - amount_in\n", "\n", "    return cal_profit\n", "\n", "\n", "def plot_k(reserve_list):\n", "    # (x + delta_x) * (y - delta_y) = xy\n", "    # delta_y = y - xy / (x + delta_x)\n", "    p_k = plot(xlim=(0, 2), ylim=(0, 2))\n", "    for reserve_in, reserve_out in reserve_list:\n", "        f = reserve_in * reserve_out / x\n", "        p = plot(f, show=False, axis_center=(0, 0))\n", "        p_k.extend(p)\n", "    p_k.show()\n", "\n", "\n", "def plot_amount_in(reserve_list):\n", "    # (x + delta_x) * (y - delta_y) = xy\n", "    # delta_y = y - xy / (x + delta_x)\n", "    p_amount_in = plot()\n", "    for reserve_in, reserve_out in reserve_list:\n", "        f = reserve_out - reserve_in * reserve_out / (x + reserve_in)\n", "        p = plot(f, show=False, axis_center=(0, 0))\n", "        p_amount_in.extend(p)\n", "    p_amount_in.show()\n", "\n", "\n", "def cal_amount_in_to_max_profit(reserve_list, with_plot=False):\n", "    cal_profit = gen_cal_profit(reserve_list)\n", "    cal_profit_f = diff(cal_profit(x), x)  # 利润函数的导数\n", "    if with_plot:\n", "        # p1 = plot(cal_profit_f, show=False, axis_center=(0, 0), label=\"profit_diff\", xlim=(-10000, 100000))\n", "        p2 = plot(cal_profit(x), show=False, axis_center=(0, 0), label=\"profit\", xlim=(-10000, 100000))\n", "        # p1.extend(p2)\n", "        # p1.show()\n", "        p2.show()\n", "    result = solve(cal_profit_f, x)\n", "    amount_in = int(result[0])\n", "    profit = cal_profit(amount_in)\n", "    return amount_in, profit\n", "\n", "\n", "def test_cal():\n", "    # f1 = gen_get_amount_out(Decimal(1030), Decimal(2000))\n", "    # f2 = gen_get_amount_out(<PERSON><PERSON><PERSON>(2010), Dec<PERSON><PERSON>(1888))\n", "    # f3 = gen_get_amount_out(Decimal(2111), Decimal(1543))\n", "    # reserve_list = [\n", "    #     [<PERSON><PERSON><PERSON>(2111), <PERSON><PERSON><PERSON>(1543)],\n", "    #     [<PERSON><PERSON><PERSON>(2010), <PERSON><PERSON><PERSON>(1888)],\n", "    #     [<PERSON><PERSON><PERSON>(1030), <PERSON><PERSON><PERSON>(2000)],\n", "    # ]\n", "    # 假设套路路径 bnb -> cake -> usdt -> bnb\n", "    reserve_list = [\n", "        [BigInt(50 * 10 ** 18), BigInt(6685 * 10 ** 18)],  # bnb/cake\n", "        [BigInt(20000 * 10 ** 18), BigInt(32880 * 10 ** 18)],  # cake/usdt\n", "        [BigInt(24300 * 10 ** 18), BigInt(115 * 10 ** 18)],  # usdt/bnb\n", "    ]\n", "    # plot_k(reserve_list)\n", "    print(\"max_profit\", cal_amount_in_to_max_profit(reserve_list))\n", "\n", "\n", "def cal_amount_out1(amount_in: any, reserve_in, reserve_out):\n", "    amount_in_with_fee = amount_in * 997\n", "    numerator = amount_in_with_fee * reserve_out\n", "    denominator = reserve_in * 1000 + amount_in_with_fee\n", "    cal_amount_out_1 = numerator / denominator\n", "    cal_profit_1 = cal_amount_out_1 - amount_in\n", "    return cal_amount_out_1, cal_profit_1\n", "\n", "\n", "def cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2):\n", "    amount_in_2, _ = cal_amount_out1(amount_in, reserve_in_1, reserve_out_1)\n", "    cal_amount_out_2, _ = cal_amount_out1(amount_in_2, reserve_in_2, reserve_out_2)\n", "    cal_profit_2 = cal_amount_out_2 - amount_in\n", "    return cal_amount_out_2, cal_profit_2\n", "\n", "\n", "def cal_amount_out3(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3):\n", "    amount_in_2, _ = cal_amount_out1(amount_in, reserve_in_1, reserve_out_1)\n", "    cal_amount_out_2, _ = cal_amount_out1(amount_in_2, reserve_in_2, reserve_out_2)\n", "    cal_amount_out_3, _ = cal_amount_out1(cal_amount_out_2, reserve_in_3, reserve_out_3)\n", "    cal_profit = cal_amount_out_3 - amount_in\n", "    return cal_amount_out_3, cal_profit\n", "\n", "\n", "def cal_amount_in_to_max_profit_fd_1(amount_in, reserve_in, reserve_out):\n", "    cal_amount_out, cal_profit = cal_amount_out1(amount_in, reserve_in, reserve_out)\n", "    cal_profit_diff = diff(cal_profit, amount_in)\n", "    return cal_profit_diff\n", "\n", "\n", "def cal_amount_in_to_max_profit_fd_2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2):\n", "    cal_amount_out, cal_profit = cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    cal_profit_diff = diff(cal_profit, amount_in)\n", "    return cal_profit_diff\n", "\n", "\n", "def get_cal_amount_in_to_max_profit_fd_2():\n", "    amount_in = symbols('amount_in')\n", "    reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2 = symbols(\n", "        'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2')\n", "    cal_amount_out, cal_profit = cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    cal_profit_diff = diff(cal_profit, amount_in)\n", "\n", "    def f(amount_in_arg, reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg):\n", "        return cal_profit_diff.subs({\n", "            amount_in: amount_in_arg,\n", "            reserve_in_1: reserve_in_1_arg,\n", "            reserve_out_1: reserve_out_1_arg,\n", "            reserve_in_2: reserve_in_2_arg,\n", "            reserve_out_2: reserve_out_2_arg,\n", "        })\n", "\n", "    def cal_profit_func(amount_in_arg, reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg):\n", "        return cal_profit.subs({\n", "            amount_in: amount_in_arg,\n", "            reserve_in_1: reserve_in_1_arg,\n", "            reserve_out_1: reserve_out_1_arg,\n", "            reserve_in_2: reserve_in_2_arg,\n", "            reserve_out_2: reserve_out_2_arg,\n", "        })\n", "\n", "    return f, cal_profit_func\n", "\n", "\n", "def get_cal_amount_in_to_max_profit_fd_inverse_2():\n", "    amount_in = symbols('amount_in')\n", "    reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2 = symbols(\n", "        'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2')\n", "    cal_amount_out, cal_profit = cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    # 计算 profit 斜率 随 amount_in 的变化，当 profit 斜率为0 时，profit 最大，计算出此时 amount_in 的值\n", "    cal_profit_diff = diff(cal_profit, amount_in)\n", "    result = solve(cal_profit_diff, amount_in)\n", "    if result:\n", "        func = result[1]\n", "        print(\"func: \\n\", func)\n", "        print(\"cal_profit: \\n\", cal_profit)\n", "\n", "        def cal_max_profit_func(reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg):\n", "            return func.subs({\n", "                reserve_in_1: reserve_in_1_arg,\n", "                reserve_out_1: reserve_out_1_arg,\n", "                reserve_in_2: reserve_in_2_arg,\n", "                reserve_out_2: reserve_out_2_arg,\n", "            })\n", "\n", "        def cal_profit_func(amount_in_arg, reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg):\n", "            return cal_profit.subs({\n", "                amount_in: amount_in_arg,\n", "                reserve_in_1: reserve_in_1_arg,\n", "                reserve_out_1: reserve_out_1_arg,\n", "                reserve_in_2: reserve_in_2_arg,\n", "                reserve_out_2: reserve_out_2_arg,\n", "            })\n", "\n", "        return cal_max_profit_func, cal_profit_func\n", "\n", "\n", "def get_cal_amount_in_to_max_profit_fd_inverse_3():\n", "    amount_in = symbols('amount_in')\n", "    reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3 = symbols(\n", "        'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2 reserve_in_3 reserve_out_3')\n", "    cal_amount_out, cal_profit = cal_amount_out3(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2,\n", "                                                 reserve_in_3, reserve_out_3)\n", "    # 计算 profit 斜率 随 amount_in 的变化，当 profit 斜率为0 时，profit 最大，计算出此时 amount_in 的值\n", "    cal_profit_diff = diff(cal_profit, amount_in)\n", "    cal_profit_diff = simplify(cal_profit_diff)\n", "    result = solve(cal_profit_diff, amount_in)\n", "    if result:\n", "        func = result[1]\n", "        print(\"func: \\n\", func)\n", "        print(\"cal_profit: \\n\", cal_profit)\n", "\n", "        def cal_max_profit_func(reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg,\n", "                                reserve_in_3_arg, reserve_out_3_arg):\n", "            return func.subs({\n", "                reserve_in_1: reserve_in_1_arg,\n", "                reserve_out_1: reserve_out_1_arg,\n", "                reserve_in_2: reserve_in_2_arg,\n", "                reserve_out_2: reserve_out_2_arg,\n", "                reserve_in_3: reserve_in_3_arg,\n", "                reserve_out_3: reserve_out_3_arg\n", "            })\n", "\n", "        def cal_profit_func(amount_in_arg, reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg,\n", "                            reserve_in_3_arg, reserve_out_3_arg):\n", "            return cal_profit.subs({\n", "                amount_in: amount_in_arg,\n", "                reserve_in_1: reserve_in_1_arg,\n", "                reserve_out_1: reserve_out_1_arg,\n", "                reserve_in_2: reserve_in_2_arg,\n", "                reserve_out_2: reserve_out_2_arg,\n", "                reserve_in_3: reserve_in_3_arg,\n", "                reserve_out_3: reserve_out_3_arg\n", "            })\n", "\n", "        return cal_max_profit_func, cal_profit_func\n", "\n", "\n", "def test_max_profit_2():\n", "    amount_in = 2.3 * 10 ** 18\n", "    # 套利路径 bnb -> cake -> bnb\n", "    reserve_in_1 = 50 * 10 ** 18\n", "    reserve_out_1 = 6685 * 10 ** 18\n", "    reserve_in_2 = 20000 * 10 ** 18\n", "    reserve_out_2 = 169 * 10 ** 18\n", "    f, cal_profit = get_cal_amount_in_to_max_profit_fd_inverse_2()\n", "    if not f:\n", "        return\n", "    max_amount_in = f(reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    max_profit = cal_profit(max_amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    print(f\"max_amount_in: {float(max_amount_in)}\\nmax_profit: {float(max_profit)}\")\n", "    profit = cal_profit(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    print(f\"amount_in: {amount_in}\\nprofit: {float(profit)}\")\n", "    plot(cal_profit(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2),\n", "         (amount_in, 0, 100000000000000000000))\n", "\n", "\n", "def test_max_profit_3():\n", "    amount_in = 2.3 * 10 ** 18\n", "    # 套利路径 bnb -> cake -> bnb\n", "    reserve_in_1 = 50 * 10 ** 18\n", "    reserve_out_1 = 6685 * 10 ** 18\n", "    reserve_in_2 = 3500 * 10 ** 18\n", "    reserve_out_2 = 6800 * 10 ** 18\n", "    reserve_in_3 = 253433 * 10 ** 18\n", "    reserve_out_3 = 1034 * 10 ** 18\n", "    f, cal_profit = get_cal_amount_in_to_max_profit_fd_inverse_3()\n", "    if not f:\n", "        return\n", "    max_amount_in = f(reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3)\n", "    max_profit = cal_profit(max_amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3,\n", "                            reserve_out_3)\n", "    print(f\"max_amount_in: {float(max_amount_in)}\\nmax_profit: {float(max_profit)}\")\n", "    profit = cal_profit(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3,\n", "                        reserve_out_3)\n", "    print(f\"amount_in: {amount_in}\\nprofit: {float(profit)}\")\n", "    amount_in = symbols(\"amount_in\")\n", "    plot(cal_profit(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3),\n", "         (amount_in, 0, 1.1*10**18))\n", "\n", "\n", "def test_fd2():\n", "    amount_in = 1 * 10 ** 18\n", "    # 套利路径 bnb -> cake -> bnb\n", "    reserve_in_1 = 50 * 10 ** 18\n", "    reserve_out_1 = 6685 * 10 ** 18\n", "    reserve_in_2 = 20000 * 10 ** 18\n", "    reserve_out_2 = 169 * 10 ** 18\n", "    f, cal_profit_func = get_cal_amount_in_to_max_profit_fd_2()\n", "    ratio = f(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    print(\"ratio: \\n\", ratio)\n", "    print(\"profit: \\n\", cal_profit_func(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2))\n", "\n", "\n", "def cal_profit_plot_2():\n", "    amount_in = symbols('amount_in')\n", "\n", "    # reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2 = symbols(\n", "    #     'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2')\n", "    reserve_in_1 = 50 * 10 ** 18\n", "    reserve_out_1 = 6685 * 10 ** 18\n", "    reserve_in_2 = 20000 * 10 ** 18\n", "    reserve_out_2 = 169 * 10 ** 18\n", "\n", "    # reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2 = symbols(\n", "    #     'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2')\n", "    cal_amount_out, cal_profit = cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    print(\"start profit plot\")\n", "    print(cal_profit)\n", "    p1 = plot(cal_amount_out, (amount_in, 0, 160 * 10 ** 18), ylim=(0, 50 * 10 ** 18), show=False, ylabel=\"amount_out\")\n", "    p2 = plot(amount_in, (amount_in, 0, 160 * 10 ** 18), show=False)\n", "    p1.extend(p2)\n", "    p1.show()\n", "    plot(cal_profit, (amount_in, 0, 10 ** 19), ylabel=\"profit\")\n", "    plot(diff(cal_profit, amount_in), (amount_in, -10 ** 20, 10 ** 19), ylabel=\"profit_diff\")\n", "    print(\"end profit plot\")\n", "\n", "\n", "def cal_profit_plot_3():\n", "    amount_in = symbols('amount_in')\n", "\n", "    # bnb -> cake -> usdt -> bnb\n", "    reserve_in_1 = 50 * 10 ** 18\n", "    reserve_out_1 = 6685 * 10 ** 18\n", "    reserve_in_2 = 3500 * 10 ** 18\n", "    reserve_out_2 = 6800 * 10 ** 18\n", "    reserve_in_3 = 253433 * 10 ** 18\n", "    reserve_out_3 = 1034 * 10 ** 18\n", "\n", "    cal_amount_out, cal_profit = cal_amount_out3(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2,\n", "                                                 reserve_in_3, reserve_out_3)\n", "    print(\"start profit plot\")\n", "    print(cal_profit)\n", "    p1 = plot(cal_amount_out, (amount_in, 0, 160 * 10 ** 18), ylim=(0, 50 * 10 ** 18), show=False, ylabel=\"amount_out\")\n", "    p2 = plot(amount_in, (amount_in, 0, 160 * 10 ** 18), show=False)\n", "    p1.extend(p2)\n", "    p1.show()\n", "    plot(cal_profit, (amount_in, 0, 10 ** 19), ylabel=\"profit\")\n", "    plot(diff(cal_profit, amount_in), (amount_in, -10 ** 20, 10 ** 19), ylabel=\"profit_diff\")\n", "    print(\"end profit plot\")\n", "\n", "\n", "def main():\n", "    amount_in = symbols('amount_in')\n", "    reserve_in, reserve_out = symbols('reserve_in reserve_out')\n", "    # (x + delta_x) * (y - delta_y) = xy\n", "    # delta_y = y - xy / (x + delta_x)\n", "    # result = cal_amount_out1(amount_in, reserve_in, reserve_out)\n", "    # print(result[0])\n", "    # print(result[1])\n", "    reserve_in_1, reserve_out_1 = symbols('reserve_in_1 reserve_out_1')\n", "    reserve_in_2, reserve_out_2 = symbols('reserve_in_2 reserve_out_2')\n", "    result = cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    print(result[0])\n", "    print(result[1])\n", "    cal_profit_2 = result[1]\n", "    print(\"cal_profit_2: \\n\", cal_profit_2)\n", "    # result = cal_amount_in_to_max_profit_fd_1(amount_in, reserve_in, reserve_out)\n", "    # print(result)\n", "    # result = cal_amount_in_to_max_profit_fd_2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    # print(\"origin:\\n\", result)\n", "    # print(\"expand:\\n\", expand(result))\n", "    # print(\"simplify:\\n\", simplify(result))\n", "    # print(\"cancel:\\n\", cancel(result))\n", "    # test_fd2()\n", "    # result = get_cal_amount_in_to_max_profit_fd_inverse_2()\n", "    # print(\"origin:\\n\", result[0])\n", "    # print(\"expand:\\n\", expand(result))\n", "    # print(\"simplify:\\n\", simplify(result))\n", "    # print(\"cancel:\\n\", cancel(result))\n", "    test_max_profit_3()\n", "    # cal_profit_plot()\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "id": "e0fa6970", "metadata": {"ExecuteTime": {"end_time": "2023-08-15T14:26:36.103214Z", "start_time": "2023-08-15T14:26:34.812186Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["func: \n", " 10000*(-100000*reserve_in_1*reserve_in_2*reserve_in_3 + 997*sqrt(9970)*sqrt(reserve_in_1*reserve_in_2*reserve_in_3*reserve_out_1*reserve_out_2*reserve_out_3))/(997*(1000000*reserve_in_2*reserve_in_3 + 997000*reserve_in_3*reserve_out_1 + 994009*reserve_out_1*reserve_out_2))\n", "cal_profit: \n", " 991026973*amount_in*reserve_out_1*reserve_out_2*reserve_out_3/((997*amount_in + 1000*reserve_in_1)*(994009*amount_in*reserve_out_1/(997*amount_in + 1000*reserve_in_1) + 1000*reserve_in_2)*(991026973*amount_in*reserve_out_1*reserve_out_2/((997*amount_in + 1000*reserve_in_1)*(994009*amount_in*reserve_out_1/(997*amount_in + 1000*reserve_in_1) + 1000*reserve_in_2)) + 1000*reserve_in_3)) - amount_in\n", "max_amount_in: 4.215989959652975e+17\n", "max_profit: 1.0474006000808826e+16\n", "amount_in: 2.2999999999999997e+18\n", "profit: -1.726271000444096e+17\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAnIAAAHWCAYAAADzS2TwAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8pXeV/AAAACXBIWXMAAA9hAAAPYQGoP6dpAABga0lEQVR4nO3deVhUZcMG8PvMDAw7yL6IIKCigoAb4m5aLr2mWWblnkuZZmmbWmqLpVmv9dZXVlYuaWqZmqlZaS6pKIrsKC6syuKCzLAOMHO+PzCK3GAcODPD/buuuYrhzJmbE8HNOed5HkEURRFEREREZHJkUgcgIiIiIv2wyBERERGZKBY5IiIiIhPFIkdERERkoljkiIiIiEwUixwRERGRiWKRIyIiIjJRLHJEREREJopFjoiIiMhEscgRERERmSizK3KHDh3C8OHD4e3tDUEQsH379ga9vqKiApMmTUJoaCgUCgVGjhx5y+00Gg1ee+01+Pn5QalUwt/fH9988829fwFERERE9aSQOoChlZaWIiwsDE899RRGjRrV4NdrtVpYW1tj9uzZ+PHHH2+73WOPPYaCggJ8/fXXCAoKQl5eHnQ63b1EJyIiImoQsytyQ4cOxdChQ2/7+b/OpG3cuBFFRUUICQnBe++9h/79+wMAbG1tsXLlSgDAkSNHUFRUdNM+9uzZg4MHDyI9PR3Ozs4AAH9/f0N/KURERER3ZHaXVu9m1qxZiI6OxqZNm5CYmIjRo0djyJAhOHfuXL33sWPHDnTt2hXLly+Hj48P2rZti5deegnl5eWNmJyIiIioLrM7I3cn2dnZWL16NbKzs+Ht7Q0AeOmll7Bnzx6sXr0a7777br32k56ejsOHD8PKygrbtm3D1atX8eyzz+LatWtYvXp1Y34JRERERLWaVZFLSkqCVqtF27Zt6zyv0Wjg4uJS7/3odDoIgoANGzbA0dERALBixQo8+uij+Oyzz2BtbW3Q3ERERES30qyKXElJCeRyOWJjYyGXy+t8zs7Ort778fLygo+PT22JA4D27dtDFEVcvHgRbdq0MVhmIiIiottpVkUuIiICWq0Wly9fRp8+ffTeT69evfDDDz+gpKSktgCePXsWMpkMLVu2NFRcIiIiojsyu8EOJSUliI+PR3x8PAAgIyMD8fHxyM7ORtu2bTF27FhMmDABW7duRUZGBmJiYrB06VLs2rWrdh+pqamIj49HYWEhVCpVnf0BwJNPPgkXFxdMnjwZqampOHToEF5++WU89dRTvKxKRERETUYQRVGUOoQhHThwAAMGDLjp+YkTJ2LNmjWoqqrCkiVLsG7dOly6dAmurq7o0aMH3nzzTYSGhgKomUokKyvrpn3881CdOXMGzz33HI4cOQIXFxc89thjWLJkCYscERERNRmzK3JEREREzYXZXVolIiIiai5Y5IiIiIhMFItcA4iiCLVaDV6NJiIiImPAItcAxcXFcHR0RHFxsdRRiIiIiFjkiIiIiEwVixwRERGRiWKRIyIiIjJRLHJEREREJopFjoiIiMhEscgRERERmSgWOSIiIiITxSJHREREZKJY5IiIiIhMFIscERERkYlikSMiIiIyUSxyRERERCaKRY6IiIjIRLHIEREREZkoFjkiIiIiE8UiR0RERGSiWOSIiIiITJRC6gBEZFp0OhHVOhFanYhqnQ5anYgqbc3HlgoZ7JQKWCr4NyIRUVNgkSNq5ko01ci4UooCdQWulWpwtaQS10oqcbVEg2ulmhv/Xgl1eRWqdTroxLvv01Ihg71SATsrBeyUNQ/7G//u7mAF3xbW8HW2ga+zDXycrGFlIW/8L5SIyAyxyBE1E0VllTh/uQTnLpf8/c+CYuSqKgyyf4VMQPWNlldZrcO16kpcK62s12s9HJTwbVFT7HxbWMPPxRYdfRwQ5GYHhZxn94iIbkcQRbEef18TAKjVajg6OkKlUsHBwUHqOES3dVldgZjMQpzMvI60/GKcu1yCqyWa227vaqeEj5MVXOyUcLG1hIudEq52lnCxs4SrnRIutko42ljAQi5AIZNBLhNgIRcgl/39MQBodSJKNNU1j4pqlGiqUFzx98fqiirkqSqQU1iOi9fLkFNYhtJK7W1zKRUytPdyQIiPA0J9HNHR2xFtPex56ZaI6AYWuQZgkSNjJIoiLl4vx/GMQsRkXENMRiEyr5XdclsfJ2sEutuhzY1H0I2Hk41lE6euIYoirpdVIaewDDnXy5BdWIacwnJcuFyC1Dw1SjTVN73GUiFDl1YtEBXogp6BLgjzdYIFz9oRUTNlskXu0KFDeP/99xEbG4u8vDxs27YNI0eOvONrDhw4gLlz5yIlJQW+vr54/fXXMWnSpHq/J4scGYtLReXYf+YyYjIKcSKzEHn/ujwqCEB7Twd0b+2MUB9HBLnbIdDdDnZK07mbQqcTkXmtFMm5aqRcUiE5V4XkS2qoyqvqbGdjKUc3f2f0DHRBz0BXdPB2qD1DSERk7kznp/q/lJaWIiwsDE899RRGjRp11+0zMjLw4IMP4plnnsGGDRuwb98+TJ06FV5eXhg8eHATJCbSnyiKSM1T4/fUAvyWUoDUPHWdzytkAjq1dET31i6IbO2Mzn4t4GhtIVFaw5DJBAS42SHAzQ4PhXkDqDkO6VdLEX3hWs0j/RoKSytx8OwVHDx7BQDgaG2Bge3dMTTEC33auHIgBRGZNZM9I/dPgiDc9Yzcq6++il27diE5Obn2uccffxxFRUXYs2dPvd6HZ+SoKVVrdYjJKMRvqQX4PbUAl4rKaz8nE4Aufi3QM9AVka2dEdGqBawtm19h0elEpBUU4+iFa4i+cBXH0wtR/I/LsTaWcgwIdsfQEE/0b+duUmckiYjqo9n8VIuOjsagQYPqPDd48GC88MILt32NRqOBRvP3DeJqtfq22xIZSmquGj+euoif4nPrDFCwspChTxs3PNDBA/cFu8PFTilhSuMgkwlo7+WA9l4OmNK7Naq1OsRmXceelHz8mpyPXFUFdiXmYVdiHiwVMvRt44ahIZ54oKMH7K1M+4wlERHQjIpcfn4+PDw86jzn4eEBtVqN8vJyWFtb3/SapUuX4s0332yqiNSMXS6uwI74XGyJvYgz+cW1zzvbWmJQe3fc38ETvYNcm+VZt4ZQyGWIDHBBZIALFv2nAxIuqrAnOR97kvOQea0Me08XYO/pAlhvl+M/nbzwePdW6NzKCYLAe+qIyDQ1myKnj/nz52Pu3Lm1H6vVavj6+kqYiMxJtVaH31ML8P3JHBw6dxXaG3OwWcplGNjeHY90bol+7dw4IlNPgiAg3NcJ4b5OeHVIO6QVFOOXpHzsTMzFhSul+CH2In6IvYh2HvZ4vLsvHo7wkWz0LhGRvppNkfP09ERBQUGd5woKCuDg4HDLs3EAoFQqoVTy8hUZ1vXSSmw8kY310Vl1JuONaOWEUZ1bYngnLxYKAxMEAcGeDgj2dMALg9rgZNZ1bIzJxu6kPKQVFOPNn1Ox9JczGBbiice7t0Jka2eepSMik9BsilxUVBR2795d57nff/8dUVFREiWi5uZ0nhprj2ZiW9wlaKp1AAAXW0s81s0Xj3ZpiUA3O4kTNg+CIKCbvzO6+Ttj8fCO2BF/CRtjcpCap8b2+Fxsj89FgKstHu/uizHdWpn86F8iMm8mO2q1pKQE58+fBwBERERgxYoVGDBgAJydndGqVSvMnz8fly5dwrp16wDUTD8SEhKCmTNn4qmnnsIff/yB2bNnY9euXfWefoSjVqmhdDoRv6UWYM3RDBxLL6x9vqO3Ayb3ao3/dPLi9BhGQBRFJF1SYWNMDnbEX6pdbcJOqcCTka3wVK/W8HS0kjglEdHNTLbIHThwAAMGDLjp+YkTJ2LNmjWYNGkSMjMzceDAgTqvmTNnDlJTU9GyZUssXLiQEwJTo6jW6rAzMQ//t/88zl8uAQDIZQKGhHhick9/dPFrwUt3RqpUU42fE3Kx+kgm0gpqBp5YyAWMDPfB0/0CEORuL3FCIqK/mWyRkwKLHN1NZbUO2+Mu4bMD52uXyXKwUmBcDz+Mj/KDl+Ot78ck4yOKIg6kXcHKgxcQk/H32dRB7T3wTL8AdPV3ljAdEVENFrkGYJGj29FUa/H9yYv4/MCF2ol7W9hYYGqfAIyP8oMD5ywzaaeyr+OLgxfwW2oB/vqJ2SPAGa8MCUbnVi2kDUdEzRqLXAOwyNG/6XQidiTk4v1f02oLnKudEk/3DcCTka1gy5UEzMqFKyX46s90/Bh7CZXamgErg9p74OXB7dDOk5dciajpscg1AIsc/dPRC1exdPcZJF1SAQA8HJSY0S8Qj3dvxQEMZu5SUTn+t/cstsRehE4EBAF4ONwHc+5vC19nG6njEVEzwiLXACxyBADnCoqx7Jcz2HfmMoCakY0z+gfiqV6tufJCM3P+cglW/J6G3Un5AGoGRTzRvRVmDQiCuwNHuRJR42ORawAWuebtcnEFPvz9HDafyIZOrBmF+mT3Vnh+UBu4ct3TZi3pogrLfz2DP89dBVCzLu7kXq0xo38g748kokbFItcALHLNU1llNb48lI4vD6Wj7Mb8YoM7euCVIcGcxJfqiL5wDct/PYO47CIANfdLzh8ajFGdfTjdDBE1Cha5BmCRa372phZg8Y6U2oEM4b5OeO3B9ujGqSfoNkRRxO+pBVi25wzSr5QCALr5t8BbI0LQ3os/N4jIsFjkGoBFrvnILSrHmz+n4NeUmvV5fZyssWBYewwL9eSZFaqXymodvj6cgY/3nUN5lRZymYAJUX6Yc39bXm4lIoNhkWsAFjnzV63VYc3RTKz4/SzKKrVQyARM7ROA5we24UAG0ktuUTmW7EqtHRDhaqfEgmHBeDiCl1uJ6N6xyDUAi5x5i8u+jgXbknE6Tw0A6OrXAu88HMr5wcgg/jx3BYt/SkH6VV5uJSLDYZFrABY581SqqcayX85g/fEsiCLgZGOB+UODMbqLL2QynjEhw9FUa/H14Qx8su987eXWST398dID7XjGl4j0wiLXACxy5udY+jW8vCUBOYU1gxke6dwSC4YFw4XTiVAj+vfl1tautnj/0U5cv5WIGoxFrgFY5MxHeaUWy389g9VHMgHUDGZ4/9FO6BnkKm0walb2p13G/B+TkK+ugCAAU3u3xosPtOPKIERUbyxyDcAiZx5OZV/HS98n1N6r9ER3XywY1h72HElIElCVV+HtnanYEnsRABDgZosPRoehc6sWEicjIlPAItcALHKmTVOtxUd7z+GLgxegE2vWRl32SCcMaOcudTQi/HGmAPN+TMLlYg1kAjCtbwDmDGrLs3NEdEcscg3AIme6Mq+W4rmNcbUL3I+K8MHi4R3haMOzcGQ8VGVVePPnFGyNuwQACHK3wwejwxDu6yRtMCIyWixyDcAiZ5p+ir+EBVuTUFqphZONBZaN6oQhIZ5SxyK6rd9TCzB/axKultScnXthUFvMHBAEOUdRE9G/sMg1AIucaSmrrMbin1Lww417j7q3dsb/Hg+Hl6O1xMmI7u56aSUW70jBjoRcAECvIBd8OCYc7vZWEicjImPCItcALHKmIzVXjVkbTyH9SilkAjB7YBs8d18bntEgk7Ml9iIWbk9GeZUWrnaW+HBMOPq0cZM6FhEZCRa5BmCRM36iKOLbY1lYsus0Kqt18HBQ4n+PR6BHgIvU0Yj0dv5yCWZ9dwpn8oshCMDM/kF4YVAbKOQyqaMRkcRY5BqARc64lWiq8cqWhNpJVgcGu+P90WFwtrWUOBnRvauo0uKtnan47ng2gJolvj5+IoK3ChA1cyxyDcAiZ7zSr5Tg6W9jce5yCSzkAuYPbY/Jvfy5KDmZnZ8TcjF/axJKNNVoYWOB/z4WhvuCPaSORUQSYZFrABY547Q3tQBzNsejWFMNDwclPhvbBV38OJkqma9/T6czrU9rvDw4GJYKXmolam5Y5BqARc646HQiPtp3Dh/vOweg5lLTp2M7c1QfNQuaai2W/fL3MnPhvk5YOa4zL7USNTMscg3AImc8VOVVmLM5Hn+cuQwAmBjlh9ce7MAzEtTs/JqSj5d/SIC6ohpu9kp8Mb4Ll/ciakZY5BqARc44nL9cgqlrTyDzWhmUChnefTgUj3RpKXUsIsnkFJZh6tqTSCsohqVchndHheJR/j9B1CywyDUAi5z0jp6/imfWx0JdUQ0fJ2t8Mb4LQnwcpY5FJLkSTTXmbo7Hb6kFAICpvVtj3tBgTlFCZOZY5BqARU5am09k47VtyajWiejcyglfTugKVzul1LGIjIZOJ+KjvWfx8R/nAQB927rhkyci4GjNNYWJzBWLXAOwyElDpxPx3q9n8MXBdADAQ2HeWP5oJ1hZyCVORmScdiXm4cUf4lFRpUOAqy1WTeyKQDc7qWMRUSNgkWsAFrmmV16pxZzN8diTUjPJ7+yBbTBnUBvOD0d0F8mXVJi+7iRyVRWwt1Lg4yciMKCdu9SxiMjAWOQagEWuaV1WV2DqupNIvKiCpVyG9x4NxcMRvIGbqL6uFGswY30sTmZdh0wA5g9tj6l9WvMPISIzwiLXACxyTedMvhqTV59AnqoCLWws8OWErujm7yx1LCKTU1mtw8Ltydh8MgcA8Ejnllg6KpRT9RCZCRa5BmCRaxoxGYWYsvYEiiuqEeBmi9WTusHPxVbqWEQmSxRFrD2aibd3nYZWJ6JPG1esHNcFdkqF1NGI6B7xTzIyKntTCzD+6+MorqhGV78W2DqjJ0sc0T0SBAGTerXG1xO7wsZSjj/PXcXjX0bjSrFG6mhEdI9Y5MhofH8yB0+vj4WmWoeBwe74dkoknGwspY5FZDb6t3PHxmk94GJrieRLajyy8igyrpZKHYuI7gGLHBmFzw9ewCtbEqHViXikc0t8Pr4LrC05vQiRoYX5OuHHGT3RytkG2YVleGTlUcTnFEkdi4j0xCJHktLpRLy7+zSW/XIGAPB03wB8MLoTLDgbPVGj8Xe1xY8zeiLUxxGFpZV44stj2J92WepYRKQH/rYkyVRpdXh5SyK+PFQz0e/8ocGYP6w9p0YgagJu9kpsmt4Dfdu6obxKi6lrT+KHGyNbich0sMiRJDTVWsxYH4sfT12EXCbgg9FheLpfoNSxiJoVW6UCX0/silGdfaDViXh5SyL+749z4GQGRKaDRY6aXEWVFtPXxWLv6ctQKmT4cnwXPNqFE/0SScFCLsN/R4dhRv+aP6Q++O0sFv2UAq2OZY7IFLDIUZMqr9RiytoTOHj2Cqwt5Fg9qRsGtveQOhZRsyYIAl4dEow3H+oIQQC+PZaFFzbHo1qrkzoaEd0Fixw1mVJNNSatjsGR89dgYynHmsnd0DPIVepYRHTDxJ7++L8nOsNCLuDnhFw8tzEOldUsc0TGjEWOmkRxRRUmfhOD4xmFsFMq8O2U7ogMcJE6FhH9y4OdvLBybBdYymX4JTkfz244BU21VupYRHQbLHLU6FTlVZjwTQxOZl2HvZUC66dGoosf100lMlaDOnjgywldoFTIsPd0AZ7+NhYVVSxzRMaIRY4aVVFZJcZ/fRxx2UVwsrHAxmk9EO7rJHUsIrqL/u3c8c2kbrCykOFA2hVMXXsS5ZUsc0TGhkWOGk1haSWeXHUciRdVcLa1xHdTeyDEx1HqWERUT72CXLFmcnfYWMpx+PxVTFodg1JNtdSxiOgfWOSoURSVVWLsV8eRmqeGq50lNk7rgQ7eDlLHIqIG6hHggm+ndIe9UoHjGYWY+E0MiiuqpI5FRDewyJHBqStq7ok7naeGq13N7PHtPO2ljkVEeuri54xvp0bCwUqBk1nXMe7rGKjKWOaIjAGLHBlUqaYak1efQOJFFVrYWGDD1EgEubPEEZm6cF8nfDetB5xsLJCQU4SxXx/D9dJKqWMRNXsscmQwFTfWa4zNug4HKwW+nRLJM3FEZiTExxEbp/WAi60lki+p8cSqY7haopE6FlGzxiJHBqGp1mL6t7GITr8GO6UCa5/qzoENRGaovZcDNk3vATd7Jc7kF+OJL4/hSjHLHJFUWOTonlVpdZj1XRwO3Vh265tJ3RDRqoXUsYiokbTxsMfm6T3g6WCFc5dLMP7r4ygq42VWIimwyNE90elEvPh9An5PLYClQoavJnZF99ac7JfI3AW42dU5Mzdx9QmUcGoSoibHIkd6E0URb/6cgh0JuVDIBHw+rjN6ce1UombD39UW66dE1g6AmLr2BFeAIGpiLHKkt0/+OI+10VkAgP8+Fob7gj0kTkRETa2dpz3WPdUddkoFjqUXYsb6WFRW66SORdRssMiRXtYfy8KK388CAN4Y3gEjwn0kTkREUunU0ql2Oa/9aVcwZ3M8qrUsc0RNgUWOGmxXYh4W/pQMAJh9XxAm9WotcSIiklr31s74YnxXWMpl2JWUh3lbk6DTiVLHIjJ7LHLUIIfPXcULm+MgisCTka0w5/62UkciIiPRr60bPn4iAnKZgC2xF/HWzlSIIsscUWNikaN6S7xYhKe/PYkqrYhhoZ54e0QIBEGQOhYRGZEhIZ54/9FOAIA1RzPx39/OSpyIyLyxyFG9XLhSgkmrT6C0UoteQS74cEw45DKWOCK62ajOLfH2yBAAwP/tP4+VBy5InIjIfLHI0V1dKdZg0uoYFJZWolNLR3wxviuUCrnUsYjIiI3v4Yd5Q4MBAO/tOYNvozOlDURkpljk6I7KK7WYuu4kcgrL4edig9WTusFOqZA6FhGZgGf6BeK5+4IAAAt/SsHPCbkSJyIyPyxydFtanYgXNschIacITjYWWD2pG1zslFLHIiITMvf+tpjU0x8A8OL3CTiWfk3aQERmhkWObuvd3afxa0oBLOUyrJrQFQFudlJHIiITIwgCFv6nA4aGeKJSq8P0dSdxtqBY6lhEZoNFjm5pzZEMfH04AwDwwWNh6ObP9VOJSD9ymYAPx4Sjq18LqCuqMfGbGOSrKqSORWQWWOToJr+nFuCtnakAgFeGtMNDYd4SJyIiU2dlIcdXE7si0M0WeaoKTFodA3VFldSxiEyeyRe5Tz/9FP7+/rCyskJkZCRiYmJuu+2aNWsgCEKdh5WVVROmNX6JF4swe2McdCLwRHdfzOgXKHUkIjITTjaWWDO5O9zslTiTX4xnvuW6rET3yqSL3ObNmzF37lwsXrwYp06dQlhYGAYPHozLly/f9jUODg7Iy8urfWRlZTVhYuN28XoZnlpzEuVVWvRt64a3OOEvERmYr3PN6HdbSzmOXriGV7YkcCkvontg0kVuxYoVmDZtGiZPnowOHTrg888/h42NDb755pvbvkYQBHh6etY+PDw8mjCx8SquqMJTa07gaokG7b0c8NnYzrCQm/S3BxEZqRAfR6wc1wUKmYDt8blY/mua1JGITJbJ/qaurKxEbGwsBg0aVPucTCbDoEGDEB0dfdvXlZSUwM/PD76+vhgxYgRSUlKaIq5R0+pEvLApHmcLSuBur8Q3k7pyrjgialR927ph2SM1S3l9fvAC1nHCYCK9mGyRu3r1KrRa7U1n1Dw8PJCfn3/L17Rr1w7ffPMNfvrpJ6xfvx46nQ49e/bExYsXb7m9RqOBWq2u8zBH7/+ahn1nLkOpqJlmxMvRWupIRNQMPNqlJV68vy0AYPGOFOxJvvXPbiK6PZMtcvqIiorChAkTEB4ejn79+mHr1q1wc3PDF198ccvtly5dCkdHx9qHr69vEydufFtPXcTnB2vWQVz+aCeE+TpJG4iImpVZ9wXhie6tIIrA85vicCr7utSRiEyKyRY5V1dXyOVyFBQU1Hm+oKAAnp6e9dqHhYUFIiIicP78+Vt+fv78+VCpVLWPnJyce85tTE5lX8e8rUkAgJkDAjEi3EfiRETU3AiCgLdHdMTAYHdoqnWYvi4Wl4rKpY5FZDJMtshZWlqiS5cu2LdvX+1zOp0O+/btQ1RUVL32odVqkZSUBC8vr1t+XqlUwsHBoc7DXOSpyjF9Xc3Q//s7eODF+9tJHYmImimFXIaPn4hAey8HXC3RYOrakyjVVEsdi8gkmGyRA4C5c+di1apVWLt2LU6fPo0ZM2agtLQUkydPBgBMmDAB8+fPr93+rbfewm+//Yb09HScOnUK48aNQ1ZWFqZOnSrVlyCJ8kotpq07iaslGgR72uPDMeGQyTjNCBFJx1apwFcTu8LVzhKn89R4YXM8pyUhqgeTHpo4ZswYXLlyBYsWLUJ+fj7Cw8OxZ8+e2gEQ2dnZkMn+7qrXr1/HtGnTkJ+fjxYtWqBLly44evQoOnToINWX0OREUcRLWxKQfEkNZ1tLrJrAEapEZBx8nKzxxfiueGLVMfyeWoD3f0vDq0OCpY5FZNQEURT5J089qdVqODo6QqVSmexl1s8OnMfyPWlQyARsmBqJyAAXqSMREdWxPe4SXtgcDwBY8VgYRnVuKW0gIiNm0pdWqWEOnb2CD25MvPnmiI4scURklEZG+GDWgCAAwLwfkxCbVShxIiLjxSLXTOQUlmH2ppo1VMd09cWT3VtJHYmI6Lbm3t8Wgzt6oFJbM5L14vUyqSMRGSUWuWagvFKLp7+NRVFZFcJaOuLNER25hioRGTWZTMCHY8LRwcsB10orMXXtSZRwJCvRTVjkzJwoinhtWxJS89RwsbXEynFdYGUhlzoWEdFd2VjWjGR1s1fiTH4xnt8YBy1HshLVwSJn5tYezcTWuEuQywR88mQEvJ24/BYRmQ5vJ2usmtAVSoUM+85cxvJfz0gdiciosMiZsZiMQizZdRoAMH9oMHoGukqciIio4cJ9nfD+6DAAwBcH07ErMU/iRETGg0XOTBWoK/DshlOo1okYHuaNKb1bSx2JiEhvD4V54+l+AQCAl7ck4GxBscSJiIwDi5wZqtbq8NzGOFwt0aCdhz3eeySUgxuIyOS9/EA79ApyQdmNAVzqiiqpIxFJjkXODH3w21nEZBTCTqnAynGdYWPJlRuIyPQp5DJ8/HgEfJyskXG1FHM3J3AZL2r2WOTMzL7TBfj84AUAwHuPdEKAm53EiYiIDMfFTomV4zrDUiHD3tMF+HT/eakjEUmKRc6M5BSWYe73CQCAST398WAnL4kTEREZXqeWTlgyMgQAsGLvWexPuyxxIiLpsMiZCU21FjO/OwVVeRXCfZ2wYFh7qSMRETWax7r6YmxkK4gi8PzGOGRdK5U6EpEkWOTMxDu7TiPxogpONhb4dGzNZQciInO2aHgHRLRygrqiGk9/G4vySq3UkYiaHH/bm4GdiblYF50FAPhwTDh8OOkvETUDSoUcK8d2gaudJc7kF2Pe1kSIIgc/UPPCImfisq+VYf6PSQCAmQMCMaCdu8SJiIiajqejFT59sjPkMgE/xedi9ZFMqSMRNSkWORNWWa3DcxtPoVhTja5+LTBnUFupIxERNbnIABe8duO+4Hd2n8ax9GsSJyJqOixyJuyD39KQcFEFR2sL/O+JCCjk/M9JRM3T5F7+GBHuDa1OxKzvTiFfVSF1JKImwd/8Jmp/2mV8eSgdAPD+o514XxwRNWuCIGDpqFAEe9rjakklZn13CtVandSxiBodi5wJyldV4MV/zBf3QEdPiRMREUnPxlKBL8Z3gb1SgZNZ1/Hf389KHYmo0bHImRitTsQLm+NQWFqJjt4OmD8sWOpIRERGw8/FFsse6QQAWHngAg5wsmAycyxyJubT/edxLL0QNpZyfPJEBJQKudSRiIiMyoOdvDAhyg8AMPf7BOSpyiVORNR4WORMyKns6/jfvnMAgCUjQ7iOKhHRbSwY1h4dvR1QWFqJ2RvjeL8cmS0WORNRoqnGC5viodWJGBnujVGdW0odiYjIaFlZyPHpk51hp1TgROZ1rOD9cmSmWORMxBs7UpBdWAYfJ2u8dWOxaCIiuj1/V1sseyQUAPDZgQs4ePaKxImIDI9FzgTsSszDltiLkAk1S3A5WFlIHYmIyCT8p5M3xveouV9uzuZ4zi9HZodFzsjlFpVj/tZEAMDMAUHo3tpZ4kRERKbltQd5vxyZLxY5I6bViZj7fTzUFdUI83XC7IFtpI5ERGRy/nm/XExmIT7cy/vlyHywyBmxVX+m10418tGYcFhwCS4iIr34u9pi6ai/75c7xPvlyEywGRip5Esq/Pe3NADAG8M7orWrrcSJiIhM2/Awb4zr0QqiWDO/3NUSjdSRiO4Zi5wRKq/UYvamOFRpRQzp6InRXTnVCBGRIbz+YAe09bDD1RINXt2SCFEUpY5EdE9Y5IzQsl9OI/1KKTwclFg6KhSCIEgdiYjILFhZyPHxExGwVMiw78xlfHssS+pIRPeERc7IHDl/FWuja36wvP9oGFrYWkqciIjIvAR7OmD+0Jp1qpfsOo20/GKJExHpj0XOiKgrqvDyDwkAgHE9WqFvWzeJExERmadJPf3Rv50bKqt1mL0xDhVVWqkjEemFRc6IvLkjFbmqCvi52GDBsPZSxyEiMluCIOCD0WFwtbNEWkExlv1yRupIRHphkTMSv6Xk48dTFyEIwH9Hh8HGUiF1JCIis+Zqp8T7o8MAAGuOZmL/mcsSJyJqOBY5I3CtRIMF25IAANP7BqCrP1dvICJqCgPauWNyL38AwMtbEnClmFOSkGlhkZOYKIp4bVsyrpZUop2HPebe31bqSEREzcqrQ4IR7GmPqyWVeOmHBOh0nJKETAeLnMS2x1/CnpR8WMgFrBgTBqVCLnUkIqJm5a8pSZQKGQ6evYI1RzOljkRUbyxyEipQV2DxTykAgOcHtkFHb0eJExERNU9tPezx+oM1g8yW/XIGqblqiRMR1Q+LnET+uqSqrqhGp5aOeKZfoNSRiIiatXE9/DCovTsqtTrM/T4emmpOSULGj0VOIjsScrH3dAEs5ALefzQMCjn/UxARSUkQBCx7pBNcbC1xJr8YH/5+TupIRHfF9iCBy8UVWLyj5pLqc/e1QTtPe4kTERERUDMlydJRoQCALw5dwInMQokTEd0Zi1wTE0URi7anoKisCh28HDCjPy+pEhEZkwc6euKRzi0hisCL3yegVFMtdSSi22KRa2K7kvKwJyUfCpmA90d3ggUvqRIRGZ3FD3WAt6MVsgvL8O7u01LHIbottogmdK1Eg0U3Rqk+OyCIo1SJiIyUg5UFPrix6sOG49nYn8ZVH8g4scg1ocU7UlBYWolgT3vMGhAkdRwiIrqDnkGutas+vLolEUVlldIGIroFFrkmsic5DzsT8yCX1YxStVTw0BMRGbtXhwQj0M0Wl4s1WHjjigqRMWGbaAJFZZV4fXvND4Cn+wYgtCUvqRIRmQIrCzlWPBYOuUzAzwm52JGQK3UkojpY5JrAkl2ncbVEgyB3O8we2EbqOERE1ABhvk61t8Ms3J6MAnWFxImI/sYi18iOnL+KLbEXIQjAe4+EwsqCa6kSEZmaWfcFIdTHEaryKrz6YyJEUZQ6EhEAFrlGVVGlxYJtSQCAcZF+6OLnLHEiIiLSh4VchhWP1dzffCDtCr4/mSN1JCIALHKN6qO955B1rQyeDlZ4ZUg7qeMQEdE9aONhj5cfqPlZvmTnaeSpyiVORMQi12hSclVY9Wc6AOCtER1hb2UhcSIiIrpXT/VujYhWTijWVGPB1iReYiXJscg1Aq1OxPytSdDqRAwN8cQDHT2ljkRERAZQM4VUJ1gqZNifdgU/nrokdSRq5ljkGsHqIxlIvKiCvZUCbz7UUeo4RERkQEHu9nhhUM0MBG/9nMJRrCQpFjkDyyksw39/OwsAmD+0PdwdrCROREREhja9TwA6tXSEuqIar21L5iVWkgyLnAGJoojXtyejvEqL7v7OeLybr9SRiIioESjkMrz/aBgs5AL2ni7gRMEkGRY5A9qRkIuDZ6/AUi7D0kdCIZMJUkciIqJG0s7THs/dV3OJdfGOFFwp1kiciJojFjkDUZVV4e2dqQCA5+4LQqCbncSJiIiosc3oH4gOXg4oKqvCop+SpY5DzRCLnIG8/9sZXC2pRJC7HZ7uFyh1HCIiagIWchneH90JCpmAX5LzsSsxT+pI1MywyBlAQk4RNhzPBgC8PSIElgoeViKi5qKjtyOe7V/zB/yin5JxrYSXWKnpsHHcI62uZoCDKAIPR/ggKtBF6khERNTEZt3XBu087HGttBJv/JwqdRxqRljk7tH6Y1lIuqSCg5UCC4a1lzoOERFJwFJRc4lVJgA/J+Ri/5nLUkeiZoJF7h5cLq7AB7+mAQBeHhIMN3ulxImIiEgqnVo6YUrv1gCA17cno1RTLXEiag5Y5O7BO7tOo1hTjU4tHfFk91ZSxyEiIonNub8tWrawxqWi8trJ4YkaE4ucno6cv4qf4nMhE4B3RoZCzjnjiIiaPRtLBZaMDAEArDmagYScImkDkdljkdODplqLhTfmCxrfww+hLR0lTkRERMaifzt3jAj3hk4E5m1NQpVWJ3UkMmMmX+Q+/fRT+Pv7w8rKCpGRkYiJibnj9j/88AOCg4NhZWWF0NBQ7N69u8HvueZIJtKvlMLVTom5D7TTNzoREZmphf/pACcbC5zOU+PrwxlSxyEzpneRq6qqQk5ODtLS0lBYWGjITPW2efNmzJ07F4sXL8apU6cQFhaGwYMH4/LlW48WOnr0KJ544glMmTIFcXFxGDlyJEaOHInk5IbNxv3FoXQAwML/tIejtcU9fx1ERGReXO2UeO3GTAYf7T2LrGulEicicyWIoijWd+Pi4mKsX78emzZtQkxMDCorKyGKIgRBQMuWLfHAAw9g+vTp6NatW2NmrhUZGYlu3brh//7v/wAAOp0Ovr6+eO655zBv3rybth8zZgxKS0uxc+fO2ud69OiB8PBwfP7553d9P5VKBScnJ/i+8D16d/DFhqmREATeG0dERDcTRRFjvzqOoxeuoXeQK76d0p2/M8jgFPXdcMWKFXjnnXcQGBiI4cOHY8GCBfD29oa1tTUKCwuRnJyMP//8Ew888AAiIyPxySefoE2bNo0WvLKyErGxsZg/fz6Amv9hSkpK0LdvXxw6dAjPPvvsTa85cuQIZs2aBbVaXftc//79sXPnzjrP/UWj0UCj+XuG7l/iswAA8upyvHxfKxQXFxv6yyIiIjMyf5AfHj57CYdSsrHhTwc8FO4jdSQyMfb29nf8A6DeZ+SeeOIJvP766+jYseMdt9NoNFi9ejUsLS3x1FNPNSxtA+Tm5sLHxwdHjx5FVFQU1Go1HB056ICIiIjMh0qlgoODw20/36BLq8bk30VOFEUUFxdj4cKFOHLkCP7444+bXuPi4oLPP/8co0ePrn1u1apVWLZsGS5cuHDT9v8+I7c//gImDO+PU4lJCPTjvHFNRa1Ww9fXFzk5OXf8ZibD4TGXBo+7NBr7uFdpdRjzRTTOFpRgeCcvLH2kk8Hfw9Twe73+7nZGrt6XVo2Nq6sr5HI5CgoKAACCIMDBwQFFRUXw8fG55TeGl5cXiouL63xOrVbD29u7Xt9IA278062FE7/xJODg4MDj3sR4zKXB4y6Nxjzu7z/ZA6NWHsWuNBUeL9CgTxu3RnkfU8Pv9XtnkOlHUlNTsXTpUqxcuRKHDh3C9evXDbHbO7K0tESXLl2wb9++2ud0Oh327duHqKioW74mKiqqzvYA8Pvvv992eyIiIkOIaNUCE6P8AQCvbUtGeaVW2kBkNgxS5B566CHY2NigtLQUX3/9NQYOHIjAwEBD7PqO5s6di1WrVmHt2rU4ffo0ZsyYgdLSUkyePBkAMGHChNrBEADw/PPPY8+ePfjvf/+LM2fO4I033sDJkycxa9asRs9KRETN20uD28HL0QrZhWX4375zUschM2GQS6uenp54/vnn6zyn1Tb+XxtjxozBlStXsGjRIuTn5yM8PBx79uyBh4cHACA7Oxsy2d9dtWfPnvjuu+/w+uuvY8GCBWjTpg22b9+OkJCQer2fUqms809qGkqlEosXL+Zxb0I85tLgcZdGUx13O6UCb40IwbR1J/HVn+l4pLMP2njYN+p7Git+rxuOQQY7LF68GP7+/rVnwszVXyNj7zaChIiI6Hamrj2JvacLENnaGZum9+DccnRPDFLkHnzwQSQnJ0Mmk6Fbt24ICwtDp06dMHz4cENkNBosckREdK9yCstw/4cHUVGlw4djwvBwREupI5EJM8g9crt27UJWVhYSExMxZ84cuLu73zSogIiIiABfZxs8d1/NhPnv7DoNVXmVxInIlN1Tkfvwww8BACkpKdBqtbC3t0dUVBSmTZuGjz76yBD5iIiIzM60PgEIdLPF1ZJK/Pe3NKnjkAm7pyIXHh4OAFiwYAE6dOiA8PBwjB07FsuWLauznikRERH9zVIhw9sjagbafXssC0kXVRInIlOlV5Hr2bMn1Go1BgyomSL3p59+QlpaGg4fPozZs2fD1dUVe/fuNWjQpvLpp5/C398fVlZWiIyMRExMzB23/+GHHxAcHAwrKyuEhoZi9+7dTZTUvDTkuK9atQp9+vRBixYt0KJFCwwaNOiu/53oZg39Xv/Lpk2bIAgCRo4c2bgBzVRDj3tRURFmzpwJLy8vKJVKtG3blj9n9NDQ4/7RRx+hXbt2sLa2hq+vL+bMmYOKigqDZuoZ5IoR4d4QReD17UnQ6kxyoaVbOnToEIYPHw5vb28IgoDt27ff9TUHDhxA586doVQqERQUhDVr1jR6TrMg6kEQBLGgoOCm51UqlfjKK6/os0ujsGnTJtHS0lL85ptvxJSUFHHatGmik5NT7deqUqlEAKJKpRJFURSPHDkiyuVycfny5WJqaqr4+uuvixYWFmJSUpKUX4bJudtx/7cnn3xS/PTTT8W4uDjx9OnT4qRJk0RHR0fx4sWLTZzcdDX0mP8lIyND9PHxEfv06SOOGDGiacKakYYed41GI3bt2lUcNmyYePjwYTEjI0M8cOCAGB8f38TJTVtDj/uGDRtEpVIpbtiwQczIyBB//fVX0cvLS5wzZ47BsxWoysWQRXtEv1d3iuuPZRp8/1LZvXu3+Nprr4lbt24VAYjbtm274/bp6emijY2NOHfuXDE1NVX85JNPRLlcLu7Zs6dpApuwBhW5Rx55RFy6dKkok8luWVZyc3NFmUxmsHBNrXv37uLMmTNrP9ZqtaK3t7e4dOlSURRvLnKPPfaY+OCDD9bZR2RkpPj00083XWgzcLfjfjfV1dWivb29uHbt2saKaHb0OebV1dViz549xa+++kqcOHEii5weGnrcV65cKQYEBIiVlZVNFdEsNfS4z5w5U7zvvvvqPDd37lyxV69ejZJv9eF00e/VnWKnN34VrxRXNMp7SKk+Re6VV14RO3bsWOe5MWPGiIMHD27EZOahQZdWW7VqhZ07d0IURYSFhcHd3R33338/XnrpJaxfvx6fffYZvLy8GuPEYaOrrKxEbGwsBg0aVPucTCbDoEGDEB0dfcvXREdH19keAAYPHnzb7elm+hz3fysrK0NVVRWcnZ0bK6ZZ0feYv/XWW3B3d8eUKVOaIqbZ0ee479ixA1FRUZg5cyY8PDwQEhKCd999t0kmXDcX+hz3nj17IjY2tvbya3p6Onbv3o1hw4Y1SsZxPfzQ0dsBqvIqLPvlTKO8h7Hj71P9NWhlhxUrVgCoWef0yJEjyM3NRVxcHOLj47Ft2zbodDosX768UYI2tqtXr0Kr1dauCvEXDw8PnDlz6/+x8vPzb7l9fn5+o+U0N/oc93979dVX4e3tfdMPAbo1fY754cOH8fXXXyM+Pr4JEponfY57eno6/vjjD4wdOxa7d+/G+fPn8eyzz6KqqgqLFy9uitgmT5/j/uSTT+Lq1avo3bs3RFFEdXU1nnnmGSxYsKBRMirkMiwZGYJRK49iS+xFPNbVF91bN68/TG/3+1StVqO8vBzW1tYSJTN+ei3RVVpaCoVCAUEQMGLECENnIqq3ZcuWYdOmTThw4ACsrKykjmOWiouLMX78eKxatQqurq5Sx2lWdDod3N3d8eWXX0Iul6NLly64dOkS3n//fRa5RnTgwAG8++67+OyzzxAZGYnz58/j+eefx9tvv42FCxc2yntGtGqBx7u1wsaYbCzcnoyds3vDQm6QqV7JzOlV5CwsLAydQ3Kurq6Qy+UoKCio83xBQQE8PT1v+RpPT88GbU830+e4/+WDDz7AsmXLsHfvXnTq1KkxY5qVhh7zCxcuIDMzs85KLTqdDgCgUCiQlpaGwMDAxg1tBvT5Xvfy8oKFhQXkcnntc+3bt0d+fj4qKythaWnZqJnNgT7HfeHChRg/fjymTp0KAAgNDUVpaSmmT5+O1157rc4a3ob0yuB2+DUlH2kFxVhzJBPT+gY0yvsYo9v9PnVwcODZuLvQ67vxxIkTGDhwIDp16oRRo0bhrbfewo4dO5CdnW3ofE3G0tISXbp0qbMihU6nw759+xAVFXXL10RFRd20gsXvv/9+2+3pZvocdwBYvnw53n77bezZswddu3Ztiqhmo6HHPDg4GElJSYiPj699PPTQQxgwYADi4+Ph6+vblPFNlj7f67169cL58+drizMAnD17Fl5eXixx9aTPcS8rK7uprP1VpsV7X9XytlrYWmLe0GAAwId7z6JAbdjpTowZf5/eA31GSLRr1068//77xU8++UScO3eueN9994nOzs6iIAiis7OzQUdjNKVNmzaJSqVSXLNmjZiamipOnz5ddHJyEvPz80VRrBlBg39NP6JQKMQPPvhAPH36tLh48WJOP6KHux338ePHi/PmzavdftmyZaKlpaW4ZcsWMS8vr/ZRXFws1Zdgchp6zP+No1b109Djnp2dLdrb24uzZs0S09LSxJ07d4ru7u7ikiVLpPoSTFJDj/vixYtFe3t7cePGjWJ6err422+/iYGBgeJjjz3W6Fm1Wp348KeHRb9Xd4pzNsU1+vs1luLiYjEuLk6Mi4sTAYgrVqwQ4+LixKysLFEURXHevHni+PHja7f/a/qRl19+WTx9+rT46aefcvqRetKryNnY2Ijnz5+/6fnMzExx+/bt9xxKSp988onYqlUr0dLSUuzevbt47Nix2s/16tWrTpETRVH8/vvvxbZt24qWlpZix44dxV27dkkR2+Td6bj369dPnDhxYu3Hfn5+IoCbHosXL2764CasIcf831jk9NfQ43706FExMjJSVCqVYkBAgPjOO++I1dXVTZza9DXkuFdVVYlvvPGGGBgYKFpZWYm+vr7is88+K16/fr1JsibkXBf95+0U/V7dKZ7MvNYk72lo+/fvv+XP6b+O88SJE8V+/frd9Jrw8HDR0tJSDAgIEFevXt3kuU2RIIoNP088aNAgLFq0CH379jXQeUHToFar4ejoCJVKBQcHB6njEBGRmZr3YyI2nchBqI8jts/sBblMkDoSGSm97pGbM2cO3nrrLRQWFho6DxERUbP30uB2sLdSIOmSCj+czJE6Dhkxvc7IyWQyCIKAFi1aYMSIEYiKikJERARCQ0PN+gZcnpEjIqKm8s3hDLy1MxXOtpbY/1J/OFqb34wRdO/0OiN3/vx5bNmyBbNmzUJhYSHeffdddOvWDfb29pwGgoiIyADGR/mhjbsdCksr8dHes1LHISOl1xm5W1Gr1UhISEBiYiJmzpxpiF0aHZ6RIyKipvTnuSsY/3UM5DIBvzzfB2097KWOREZGryJXWFjYLNe1ZJEjIqKm9vS3J/FrSgF6Bblg/ZRICAIHPtDf9FrZwdXVFT4+PggLC6vzaNu2Lb/BiIiIDOj1Bztgf9oVHDl/Db+mFGBICFcPor/pdY9cUlISli1bhg4dOuDEiROYOXMmOnToADs7O0RGRho6IxERUbPl62yDZ24s17VkVyoqqrQSJyJjoleR69ixI8aOHYvly5fjt99+w+XLl7Fz5054eXlh4MCBhs5IRETUrD3TPxBejla4eL0cXx5KlzqO2Tt06BCGDx8Ob29vCIKA7du3N+j1FRUVmDRpEkJDQ6FQKDBy5MhbbrdhwwaEhYXBxsYGXl5eeOqpp3Dt2rUGvZdBVv4VBAFDhw7F+vXrkZ+fb4hdEhER0Q02lgosGNYeAPDZgfO4VFQucSLzVlpairCwMHz66ad6vV6r1cLa2hqzZ8/GoEGDbrnNkSNHMGHCBEyZMgUpKSn44YcfEBMTg2nTpjXovQxS5P7So0cP7N+/35C7JCIya3PmzMGoUaOkjkEm4D+dvNC9tTMqqnR4d/dpqeOYtaFDh2LJkiV4+OGHb/l5jUaDl156CT4+PrC1tUVkZCQOHDhQ+3lbW1usXLkS06ZNg6fnre9pjI6Ohr+/P2bPno3WrVujd+/eePrppxETE9OgrHoVOTs7O0RFReGZZ57BZ599hiNHjuDatWvYs2cPiouL9dklEVGzFBMTg65du0odg0yAIAh4Y3hHyARgV2Ieoi807BIcGc6sWbMQHR2NTZs2ITExEaNHj8aQIUNw7ty5eu8jKioKOTk52L17N0RRREFBAbZs2YJhw4Y1KIteRW7Lli0YMWIEioqK8L///Q/9+vWDm5sb/vOf/+DFF1/UZ5dERHpZvHgxQkNDYWtrCw8PD8yYMQNVVVUAgMzMTAiCgB9//BF9+/aFtbU1unXrhuzsbPz555/o0aMHbGxsMHDgQBQVFdXZb3JyMoYNGwYHBwd4enrixRdfRGVlJQBgz549sLW1hU6nq7O9IAi4evUqAODChQsQBAE7d+7EwIEDYWNjg3bt2uH48eMAgMrKSlhYWODo0aN47bXXIAgCevTo0QRHjExZB28HjI30AwC8+XMKqrW6u7yCDC07OxurV6/GDz/8gD59+iAwMBAvvfQSevfujdWrV9d7P7169cKGDRswZswYWFpawtPTE46Ojg2+nKtXkRsyZAjmzZuHTZs2IS0tDWq1GklJSbh48SLmz5+vzy6JiBpMFEWIoogvvvgCqampWLNmDX788Ud89dVXAICEhAQAwMqVK/Huu+/i6NGjKCgowLhx47Bs2TL83//9H/bv34+EhIQ6P4Dj4uLQs2dPdO7cGadOncKmTZuwceNGvPfee7WfDwkJgUz294/Q+Ph4eHt7w9XVtfa9BUHAihUrsHDhQiQkJKBVq1aYN28eAEChUODIkSO1r83Ly8OePXsa/6CRyZt7f1s42VjgTH4xNsZkSx2n2UlKSoJWq0Xbtm1hZ2dX+zh48CAuXLhQ7/2kpqbi+eefx6JFixAbG4s9e/YgMzMTzzzzTIPy6DWPXHV1Nd577z3s3LkTGo0GoaGhGDduHDp27KjP7oiI9CIIAt56663aj/38/DBo0CCkpaUBqClIzs7O2Lx5M1xcXAAA/fr1w+HDh5GSkgIbGxsAQLdu3eoM1Jo2bRrGjx+PJUuWAACCgoIwefJk7Ny5EwsXLkR8fDzCwsLqZElISKjzXEJCApycnLB582a4ubkBAB566CF88cUXAGrWrM7NzYWLi0ud1/3xxx+Ii4vj1Q26rRa2lph7f1ss+ikFH+49h4fCfbgOaxMqKSmBXC5HbGws5HJ5nc/Z2dnVez9Lly5Fr1698PLLLwMAOnXqBFtbW/Tp0wdLliyBl5dXvfaj1xm5efPm4bPPPsPAgQMxcuRIaDQa/Oc//8HkyZNhoBW/iIjuKisrCzNnzkRISAhatGgBOzs7fP/992jZsiWAmjL18MMP15Y4oOayyJgxY2pL3F/PtW7dGgBw5swZxMbG4rnnnqvzXpaWltBoNABqzsj9e13pf5e7hIQEjBgxorbEAUBGRgaCgoJqP46Li7upEN53330scXRXT3ZvhaAb67B+tv+81HGalYiICGi1Wly+fBlBQUF1Hrcb2HArZWVldc7qA6gthg3pUnoVue+++w4bN27EkiVLsGjRImzatAlnzpxBTEwMPvjgA312SUTUIFeuXEG3bt1w7do1rFixAocPH8bRo0chk8lqy1F8fPxNk5QnJCTUuRetoqICaWlpta9JSUmBhYUF2rZtW+d1qampCA0NRWlpKS5cuFCngOl0uptKWUJCAqKioursIz4+HuHh4XU+/neRe+ihh5CUlAQAGDZsGBYtWoRevXohICAAycnJDT1MZKYUchleuzEdyeojmci+ViZxIvNSUlKC+Ph4xMfHA6j5Iyw+Ph7Z2dlo27Ytxo4diwkTJmDr1q3IyMhATEwMli5dil27dtXuIzU1FfHx8SgsLIRKpaqzPwAYPnw4tm7dipUrVyI9PR1HjhzB7Nmz0b17d3h7e9c/rKgHBwcH8cKFCzc9v2/fPjEoKEifXZoElUolAhBVKpXUUYiava+//lp0dnYWdTpd7XOffPKJCEC8fPmyqFKpREEQxBMnTtR+Pj09XQQgZmZm1j4XExMjymQysbi4WBRFUfz1119FmUwmVlRU1HmdhYWF+Msvv4hJSUm17/GX3bt3iwDE1NRUURRFsaioSAQgxsTE1MncokULcdu2bbUft27dWly7dm2dbdq0aSNWVlaKoiiKvr6+4qpVq0RRFMUPP/xQXLJkiV7HisyTTqcTx311TPR7dac4Y/1JqeOYlf3794sAbnpMnDhRFEVRrKysFBctWiT6+/uLFhYWopeXl/jwww+LiYmJtfvw8/O75T7+6eOPPxY7dOggWltbi15eXuLYsWPFixcvNiirXmfkevfujbVr1970fOvWrZGbm6vPLomIGsTFxQVqtRo7duzAuXPnsGLFCrz55pvw8fGBm5sbEhISIJfLERISUvuav+6Z8/Pzq/NcYGBg7b0tkZGRcHJywrx585Ceno4//vgDDz74IB5//HEMGTIELi4uEAQBJ06cAAAcO3YMs2bNgpWVVe1ZvMTERCgUCoSGhta+T1ZWFq5fv17njJxOp0NaWhpyc3OhUqlQXFwMKysrWFhYQK1WQxAETJ06FQBQVVUFJyenxjqcZIIEQcBrD7aHTAB2J+UjJqNQ6khmo3///rWDqf75WLNmDQDAwsICb775JjIyMlBZWYnc3Fxs3bq1zv/zmZmZt9zHPz333HNISUlBWVkZcnNzsX79evj4+DQoq15F7r333sNHH32E2bNn186ZUlVVhU8++QQdOnTQZ5dERA0yfPhwTJkyBePHj0fv3r1x6dIlPPbYY7VFKSEhAe3atYOVlVXtaxISEhAREVFnP/8epODo6Ijt27fj0KFD6NixI6ZNm4YJEybUjmr18vLC22+/jXHjxsHPzw+ff/45Ro8ejZCQkNr7W2713nFxcXBycoK/v3/tc0uWLMGaNWvg4+ODJUuWICUlpXbQWHJyMrp161a7bVJSEgeU0U2CPR0wplsrADXrsOp0vE+9uRHEf9fDeoqLi8P06dMRGxsLS0tLaLVaODk5Yfv27ejVq5ehcxoFtVoNR0dHqFQqODg4SB2HiMzMqlWrcOXKFSxYsABffvklCgoKsHDhQgA1N1jv3bu3zsANIgC4UqzBgA8OoERTjQ/HhOHhiJZSR6ImpPcSXREREThx4gRSU1Px3XffYffu3bhw4YLZljgiosaWlJRUeyk4OTm5dmRsdXU1ioqKWOLoltzslXh2QCAAYPmeNJRXaiVORE1J7zNyzRHPyBERkTGqqNJi4H8P4lJROebe3xazB7aROhI1Eb2KnFqtxurVq5Gfn4/WrVsjLCwMoaGhdeZlMkcsckREZKx2JORi9sY4WFvIceDl/vBwsLr7i8jk6VXkBg0ahISEhNo1C/+aRT0wMBBhYWHYvHmzwYMaAxY5IiIyVqIoYtTKo4jLLsLoLi3x/uiwu7+ITJ5e98hFR0dj9+7d2L17N5KTk1FSUoLo6Gi89NJLcHd3N3RGIiIiugtBELDwPzUzR2w5dRHJl1QSJ6KmoNdaq506dYJC8fdLlUolunbtiq5duxosGBERETVM51YtMDzMGz8n5GLJrlRsnNYDgiBIHYsakV5n5JYvX45FixbVrjtIRERExuHVIe1gqZDhWHoh9p6+LHUcamR6FTl/f3+o1Wp06NABCxYswI4dO5CTk2PobERERNRALVvYYErv1gCA9/acQbVWJ3Eiakx6FblHHnkEmZmZ6NWrF44ePYqJEyfC398fbm5ueOCBBwydkYiIiBrgmX6BcLKxwPnLJfgh9qLUcagR6XWPXHJyMqKjo+ssa5OZmYm4uDgkJiYaLBwRERE1nKO1BZ67rw3e3pmKFb+fxYhwb9hY6vUrn4ycXmfkunXrhtLS0jrP+fv74+GHH8bixYsNEoyIiIj0N65HK/g6W+NKsQZf/ZkhdRxqJHoVueeffx5vvPEGioqKDByHiIiIDEGpkOPlwcEAgC8OXsCVYg5QNEd6TQgsk9X0PxcXFzz88MOIjIxEREQEQkJCYGlpafCQxoITAhMRkSnR6USM/OwIEi+qML6HH94eGSJ1JDIwvYpcVlYWEhISEB8fX/vPzMxMKBQKtGvXzmzvk2ORIyIiU3P0wlU8ueo45DIBv8/piwA3O6kjkQHpVeRupbi4GPHx8UhMTMTMmTMNsUujwyJHRESm6Kk1J/DHmcsYGuKJleO6SB2HDEivIqfVavHVV18hLS0NLVu2RHh4OMLCwuDi4tIYGY0GixwREZmitPxiDP3fIehE4McZPdHFr4XUkchA9Brs8Nxzz2HRokUoKCjAq6++iqFDh8Ld3R2tWrXCQw89ZOiMREREdA/aedpjdBdfAMDS3adhoItxZAT0KnJbt27FunXrsGHDBlhZWeHkyZP43//+h4qKCvj5+Rk6IxEREd2jOfe3hZWFDCezruO31AKp45CB6FXkSkpK0KFDBwCAhYUFFAoFZs2ahfnz58PKysqgAYmIiOjeeTpaYWrvAADAe7+cQRWX7jILehW5gIAA5ObmAgB8fHxw6dIlAMDw4cOxfv16w6UjIiIig3m6XwCcbS2RfrUUm09wjXRzoFeRGzVqFH755RcAQL9+/fDNN98AAFJTU1FeXm64dERERGQw9lYWmH1fEADgf/vOobxSK3Eiulf3PP1IdnY2unXrBp1OB7VajSlTpuCzzz4zVD6jwlGrRERk6iqrdbjvvwdw8Xo5XhnSDs/2D5I6Et0Dg8wjd/XqVfz8889wcXHB8OHDIQiCIbIZHRY5IiIyB1tPXcTc7xPgYKXAn6/cB0cbC6kjkZ4MNiFwc8AiR0RE5kCrEzHsf38iraAYM/oH4tUhwVJHIj3V+x657OzsBu34rwEQREREZFzkMgEvD24HAFh9JAMF6gqJE5G+6l3kunXrhqeffhonTpy47TYqlQqrVq1CSEgIfvzxR4MEJCIiIsMb2N4dXfxaoKJKh4/3nZM6DulJUd8NH3zwQdjZ2eH++++HlZUVunTpAm9vb1hZWeH69etITU1FSkoKOnfujOXLl2PYsGGNmZuIiIjugSAIeGVwO4z58hg2ncjB1D4BaO1qK3UsaqB63yNnaWmJnJwc2Nvbw83NDU888QSuXbuG8vJyuLq6IiIiAoMHD0ZISEhjZ5YM75EjIiJzM2l1DA6kXcHwMG988kSE1HGogepd5Pz9/fHFF19g8ODBkMlkyM/Ph7u7e2PnMyosckREZG5SclV48OPDAIBds3ujo7ejxImoIep9j9yLL76I4cOHo0+fPhAEARs2bMCJEyc4ATAREZEJ6+jtiIfCvAEA7/+aJnEaaqgGTT+SmJiIn3/+GQsXLkRAQAAyMzMhCAKCgoIQFhaG8PBwhIWFYejQoY2ZWTI8I0dEROYo82opBq04iGqdiE3Te6BHgIvUkaie9JpHrk2bNoiOjoatrS0SExMRHx9f+0hOTkZxcXFjZJUcixwREZmr17YlYcPxbHRu5YQfZ/Q028n9zY1ea62eO3cOrq6usLa2RmRkJJ5++mmsXLkS0dHRUKvVhs54S4WFhRg7diwcHBzg5OSEKVOmoKSk5I6v6d+/PwRBqPN45plnmiQvERGRMZs9sA2sLGQ4lV2EfacvSx2H6kmvIncnTdXgx44di5SUFPz+++/YuXMnDh06hOnTp9/1ddOmTUNeXl7tY/ny5U2QloiIyLh5OFhhcq/WAGruldPquPCTKTB4kWsKp0+fxp49e/DVV18hMjISvXv3xieffIJNmzYhNzf3jq+1sbGBp6dn7YOXSImIiGo80zcQDlYKpBUUY2finX+fknEwySIXHR0NJycndO3atfa5QYMGQSaT4fjx43d87YYNG+Dq6oqQkBDMnz8fZWVlt91Wo9FArVbXeRAREZkrRxsLTO8bAAD4aO85VGt1EieiuzHJInerOewUCgWcnZ2Rn59/29c9+eSTWL9+Pfbv34/58+fj22+/xbhx4267/dKlS+Ho6Fj78PX1NdjXQEREZIwm9WoNZ1tLZFwtxdY4rptu7IyqyM2bN++mwQj/fpw5c0bv/U+fPh2DBw9GaGgoxo4di3Xr1mHbtm24cOHCLbefP38+VCpV7SMnJ0fv9yYiIjIFdkoFnulXc1bu433nUFnNs3LGrN5rrTaFF198EZMmTbrjNgEBAfD09MTly3VH1FRXV6OwsBCenp71fr/IyEgAwPnz5xEYGHjT55VKJZRKZb33R0REZA7G9/DHqj8zcPF6Ob4/mYNxPfykjkS3YVRFzs3NDW5ubnfdLioqCkVFRYiNjUWXLl0AAH/88Qd0Ol1tOauP+Ph4AICXl5deeYmIiMyRtaUcswYEYfGOFPzfH+fxaJeWsLKQSx2LbsGoLq3WV/v27TFkyBBMmzYNMTExOHLkCGbNmoXHH38c3t41y4xcunQJwcHBiImJAQBcuHABb7/9NmJjY5GZmYkdO3ZgwoQJ6Nu3Lzp16iTll0NERGR0Hu/uC29HK+SrK/Dd8Wyp49BtmGSRA2pGnwYHB2PgwIEYNmwYevfujS+//LL281VVVUhLS6sdlWppaYm9e/figQceQHBwMF588UU88sgj+Pnnn6X6EoiIiIyWUiHHcwPbAAA+O3AeZZXVEieiW9Fria7mikt0ERFRc1Kl1WHgfw8iu7AMrw4Jxoz+N99PTtIy2TNyRERE1Lgs5DK8MKjmrNwXhy6guKJK4kT0byxyREREdFsjwn0Q6GaLorIqfHM4U+o49C8sckRERHRbcpmAOfe3BQB89Wc6isoqJU5E/8QiR0RERHc0LMQLwZ72KNZU48tD6VLHoX9gkSMiIqI7kskEvPhAOwDAmqOZuFqikTgR/YVFjoiIiO5qUHt3hLV0RFmlFp8fuPXSltT0WOSIiIjorgRBwNwbZ+W+PZaFAnWFxIkIYJEjIiKieurbxhVd/VpAU63D5wd5Vs4YsMgRERFRvQiCgBcG1Yxg/e54Ni7zrJzkWOSIiIio3noFuaBL7Vk5jmCVGoscERER1VvNWbma1R42HM/C5WKelZMSixwRERE1SO8gV3Ru5QRNtQ5f8KycpFjkiIiIqEEEQcDzN+6V23A8C1eKOa+cVFjkiIiIqMH6tnFFRCsnVFTp8OUhjmCVCoscERERNZggCHh+YM29ct8e41k5qbDIERERkV76tXVDmG/NWblVf/JeOSmwyBEREZFe/jmC9dvoLK7BKgEWOSIiItJb/7ZuCGvpiPIqLVYd4lm5psYiR0RERHqrGcFac1ZuXXQWrvGsXJNikSMiIqJ7MqCdOzrdOCv3Je+Va1IsckRERHRP6oxgjc5CYWmlxImaDxY5IiIiumf3Bbsj1McRZZVajmBtQixyREREdM/+eVZu7dFMnpVrIixyREREZBAD27sjxMcBZZVafMWzck2CRY6IiIgMQhAEzL7v77Ny13lWrtGxyBEREZHB3N/BAx28HFBaqcVXh3lWrrGxyBEREZHB/HNeubVHs6Aqq5I4kXljkSMiIiKDeqCDB4I97VGiqcaao5lSxzFrLHJERERkUIIg4NkBQQCA1UczUKqpljiR+WKRIyIiIoN7MNQLrV1tUVRWhQ3Hs6SOY7ZY5IiIiMjg5DIBM/oHAgBW/ZmBiiqtxInME4scERERNYqHI3zg42SNK8Ua/HAyR+o4ZolFjoiIiBqFhVyGp/sFAAA+P5iOKq1O4kTmh0WOiIiIGs1jXX3haqfEpaJybIu7JHUcs8MiR0RERI3GykKOaX1aAwBWHrgArU6UOJF5YZEjIiKiRjW2hx8crS2QcbUUu5PypI5jVljkiIiIqFHZKRV4qlfNWblP95+HKPKsnKGwyBEREVGjm9TTH3ZKBc7kF2Pf6ctSxzEbLHJERETU6BxtLDCuhx8A4P94Vs5gWOSIiIioSUzp3RpKhQzxOUU4euGa1HHMAoscERERNQk3eyWe6N4KAPDJH+ckTmMeWOSIiIioyUzvGwALuYBj6YWIzSqUOo7JY5EjIiKiJuPtZI1RES0BAP/3x3mJ05g+FjkiIiJqUjP6B0ImAPvTriD5kkrqOCaNRY6IiIialL+rLYaHeQMAPjvAs3L3gkWOiIiImtyz/YMAAL8k5yP9SonEaUwXixwRERE1uXae9hgY7A5RBFb9mS51HJPFIkdERESSeKZ/IADgx9hLuKyukDiNaWKRIyIiIkl083dGF78WqNTq8M2RTKnjmCQWOSIiIpLMM/1qzsptOJYFdUWVxGlMD4scERERSWZgsDvauNuhWFON745nSx3H5LDIERERkWRkMgHT+wYAAL45nAFNtVbiRKaFRY6IiIgkNSLcB16OVrhcrMG2U5ekjmNSWOSIiIhIUpYKGab0bg0A+PJQOrQ6UeJEpoNFjoiIiCT3ePdWcLBSIP1qKX5PzZc6jslgkSMiIiLJ2SkVmBDlDwBYeTAdosizcvXBIkdERERGYVIvfygVMiTkFOFYeqHUcUwCixwREREZBVc7JUZ3bQkA+PzgBYnTmAYWOSIiIjIa0/sEQiYAB89eQWquWuo4Ro9FjoiIiIxGKxcbDAv1AgB8cYhn5e6GRY6IiIiMyl/Ldu1MzENOYZnEaYwbixwREREZlRAfR/Rp4wqtTsRXf6ZLHceoscgRERGR0fnrrNzmkzm4VqKROI3xYpEjIiIio9Mz0AWhPo6oqNJhbXSW1HGMFoscERERGR1BEGrPyq2LzkRZZbXEiYwTixwREREZpSEhnvB3sUFRWRU2xeRIHccomWSRe+edd9CzZ0/Y2NjAycmpXq8RRRGLFi2Cl5cXrK2tMWjQIJw7d65xgxIREZHe5DIB0/oGAAC+PpyBKq1O4kTGxySLXGVlJUaPHo0ZM2bU+zXLly/Hxx9/jM8//xzHjx+Hra0tBg8ejIqKikZMSkRERPfikc4t4WqnxKWicvyckCt1HKNjkkXuzTffxJw5cxAaGlqv7UVRxEcffYTXX38dI0aMQKdOnbBu3Trk5uZi+/btjRuWiIiI9GZlIcfkXv4AgFV/ZkAURWkDGRmTLHINlZGRgfz8fAwaNKj2OUdHR0RGRiI6OlrCZERERHQ3YyNbwdpCjtN5ahy9cE3qOEalWRS5/Px8AICHh0ed5z08PGo/dysajQZqtbrOg4iIiJqWk40lRndtCQBYxQmC6zCaIjdv3jwIgnDHx5kzZ5o009KlS+Ho6Fj78PX1bdL3JyIiohpTereGIAAH0q7gXEGx1HGMhtEUuRdffBGnT5++4yMgIECvfXt6egIACgoK6jxfUFBQ+7lbmT9/PlQqVe0jJ4dDn4mIiKTg52KLwR1qfmd/9WeGxGmMh0LqAH9xc3ODm5tbo+y7devW8PT0xL59+xAeHg4AUKvVOH78+B1HviqVSiiVykbJRERERA0zrW9r7EnJx7a4S3hpcDu42fN3tNGckWuI7OxsxMfHIzs7G1qtFvHx8YiPj0dJSUntNsHBwdi2bRuAmtmhX3jhBSxZsgQ7duxAUlISJkyYAG9vb4wcOVKir4KIiIgaooufMyJaOaFSq8O30ZlSxzEKRnNGriEWLVqEtWvX1n4cEREBANi/fz/69+8PAEhLS4NKpard5pVXXkFpaSmmT5+OoqIi9O7dG3v27IGVlVWTZiciIiL9TesTgGc3nMK3x7Iwo38QrC3lUkeSlCByQpZ6U6vVcHR0hEqlgoODg9RxiIiImh2tTkT/D/Yjp7AcS0aGYFwPP6kjScokL60SERFR8ySXCXiqV2sAwDeHM6DTNe/zUSxyREREZFIe6+oLBysF0q+WYt+Zy1LHkRSLHBEREZkUW6UCT0bWXFJt7hMEs8gRERGRyZnU0x8KmYCYjEIk5BRJHUcyLHJERERkcjwdrfBQmDeA5n1WjkWOiIiITNLUPjUrPv2SnI+L18skTiMNFjkiIiIySR28HdAryAVanYjVRzKljiMJFjkiIiIyWX+dldt8IgfqiiqJ0zQ9FjkiIiIyWf3buqGNux1KNNXYFJMtdZwmxyJHREREJksQBEy7cVZu9ZFMVGl1EidqWixyREREZNJGRHjD1U6JPFUFdiflSR2nSbHIERERkUlTKuSYGPX3BMHNaRl5FjkiIiIyeeN6+EGpkCH5khons65LHafJsMgRERGRyWtha4lRnX0AAN8czpA4TdNhkSMiIiKzMLlXawDArynNZ4JgFjkiIiIyC2097NGnjSt0IrAuOkvqOE2CRY6IiIjMxuRe/gCAjTHZKNVUSxumCbDIERERkdno39YdrV1tUVxRjR9PXZQ6TqNjkSMiIiKzIZMJtWfl1hzJhE5n3lORsMgRERGRWXmkc0vYWymQfrUUB89ekTpOo2KRIyIiIrNiq1Tg8W6+AIBvjpj3VCQsckRERGR2JkT5QyYAf567irMFxVLHaTQsckRERGR2fJ1t8EAHTwDA6iOZ0oZpRCxyREREZJae6l0zQfDWUxdxvbRS4jSNg0WOiIiIzFI3/xYI8XGAplqH72KypY7TKFjkiIiIyCwJgoCnbizb9W10Fqq0OokTGR6LHBEREZmtBzt5wdVOiXx1BX5Jzpc6jsGxyBEREZHZUirkGN/DDwDwzWHzm4qERY6IiIjM2tgerWAplyE+pwinsq9LHcegWOSIiIjIrLnaKfFQuDcA85uKhEWOiIiIzN5f66/uTspDnqpc2jAGxCJHREREZq+jtyN6BDhDqxOxLjpL6jgGwyJHREREzcLkG1ORbIrJRkWVVuI0hsEiR0RERM3CoPYe8HGyxvWyKuxIyJU6jkGwyBEREVGzIJcJGB9VMxXJ2qOZEEVR4kT3jkWOiIiImo0xXX2hVMiQkqs2i6lIWOSIiIio2Whha4kRN6YiWXPU9Ac9sMgRERFRszIhyh8A8EtSHgrUFdKGuUcsckRERNSshPg4oqtfC1TrRHx3PFvqOPeERY6IiIianYk9/QEA38Vko7JaJ22Ye8AiR0RERM3OkBBPuNsrcaVYg1+S86SOozcWOSIiImp2LOQyjI38eyoSU8UiR0RERM3SE5G+sJALOJVdhKSLKqnj6IVFjoiIiJold3srPBjqBQBYY6Jn5VjkiIiIqNmacGPQw8+JubhWopE2jB5Y5IiIiKjZivB1QqeWjqis1mHTiRyp4zQYixwRERE1W4IgYOKNCYI3HMtCtda0piJhkSMiIqJm7cFOXnCxtUSuqgK/pxZIHadBWOSIiIioWbOykOPx7r4AgLXRmdKGaSAWOSIiImr2xvXwg1wm4Fh6Ic7kq6WOU28sckRERNTseTlaY3BHDwDA2qNZEqepPxY5IiIiIgATbgx62B53CaqyKmnD1BOLHBERERGAyNbOCPa0R3mVFltOXZQ6Tr2wyBERERGhZiqS8VE166+uP5YFnU6UONHdscgRERER3TAy3Ad2SgUyrpbi6IVrUse5KxY5IiIiohtslQo80tkHAPDtsUxpw9QDixwRERHRP4zrUXN59ffUAuSpyiVOc2csckRERET/0MbDHj0CnKETgY3Hs6WOc0csckRERET/Mr6HPwBg44kcVFYb7/qrLHJERERE//JARw+42StxpViD31LzpY5zWyxyRERERP9iIZfhie6tAADfRhvvSg8sckRERES38ER3X8hlAo5nFOJsQbHUcW6JRY6IiIjoFrwcrTGovTuAmgmCjRGLHBEREdFt/DXoYeupSyjRVEsb5hZY5IiIiIhuo2egCwJcbVGiqcb2uEtSx7kJixwRERHRbchkAsb2+Hv9VVE0rvVXWeSIiIiI7uDRzi1hZSHDmfxinMy6LnWcOkyyyL3zzjvo2bMnbGxs4OTkVK/XTJo0CYIg1HkMGTKkcYMSERGRyXO0scCIsBvrrxrZVCQmWeQqKysxevRozJgxo0GvGzJkCPLy8mofGzdubKSEREREZE7GR9VcXv0lOQ9XijUSp/mbQuoA+njzzTcBAGvWrGnQ65RKJTw9PRshEREREZmzEB9HhPs6IT6nCN+fzMHMAUFSRwJgomfk9HXgwAG4u7ujXbt2mDFjBq5du3bH7TUaDdRqdZ0HERERNU/jbwx62HAsC1qdcQx6aDZFbsiQIVi3bh327duH9957DwcPHsTQoUOh1Wpv+5qlS5fC0dGx9uHr69uEiYmIiMiYPNjJC042FshVVSAmo1DqOAAAQTSScbTz5s3De++9d8dtTp8+jeDg4NqP16xZgxdeeAFFRUUNfr/09HQEBgZi7969GDhw4C230Wg00Gj+vg6uVqvh6+sLlUoFBweHBr8nERERmbY9yXlo5WyLDt7G0QOM5h65F198EZMmTbrjNgEBAQZ7v4CAALi6uuL8+fO3LXJKpRJKpdJg70lERESmbUiIl9QR6jCaIufm5gY3N7cme7+LFy/i2rVr8PIyrv8gRERERPVlkvfIZWdnIz4+HtnZ2dBqtYiPj0d8fDxKSkpqtwkODsa2bdsAACUlJXj55Zdx7NgxZGZmYt++fRgxYgSCgoIwePBgqb4MIiIiontiNGfkGmLRokVYu3Zt7ccREREAgP3796N///4AgLS0NKhUKgCAXC5HYmIi1q5di6KiInh7e+OBBx7A22+/zUunREREZLKMZrCDKVCr1XB0dORgByIiIjIKJnlplYiIiIhY5IiIiIhMFoscERERkYlikSMiIiIyUSxyRERERCaKRY6IiIjIRLHIEREREZkoFjkiIiIiE8UiR0RERGSiWOSIiIiITBSLHBEREZGJYpEjIiIiMlEsckREREQmikWOiIiIyEQJoiiKUocwFaIoori4GPb29hAEQeo4RERE1MyxyBERERGZKF5aJSIiIjJRLHJEREREJopFjoiIiMhEscgRERERmSgWOSIiIiITxSJHREREZKJY5IiIiIhM1P8DcH7ydBWk14QAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["test_max_profit_3()"]}, {"cell_type": "code", "execution_count": 3, "id": "ab349578", "metadata": {}, "outputs": [], "source": ["def cal_amount_out4(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3, reserve_in_4, reserve_out_4):\n", "    amount_in_2, _ = cal_amount_out1(amount_in, reserve_in_1, reserve_out_1)\n", "    cal_amount_out_2, _ = cal_amount_out1(amount_in_2, reserve_in_2, reserve_out_2)\n", "    cal_amount_out_3, _ = cal_amount_out1(cal_amount_out_2, reserve_in_3, reserve_out_3)\n", "    cal_amount_out_4, _ = cal_amount_out1(cal_amount_out_3, reserve_in_4, reserve_out_4)\n", "    cal_profit = cal_amount_out_4 - amount_in\n", "    return cal_amount_out_4, cal_profit\n", "\n", "\n", "def get_cal_amount_in_to_max_profit_fd_inverse_4():\n", "    amount_in = symbols('amount_in')\n", "    reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3, reserve_in_4, reserve_out_4 = symbols(\n", "        'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2 reserve_in_3 reserve_out_3 reserve_in_4 reserve_out_4')\n", "    cal_amount_out, cal_profit = cal_amount_out4(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2,\n", "                                                 reserve_in_3, reserve_out_3, reserve_in_4, reserve_out_4)\n", "    # 计算 profit 斜率 随 amount_in 的变化，当 profit 斜率为0 时，profit 最大，计算出此时 amount_in 的值\n", "    cal_profit_diff = diff(cal_profit, amount_in)\n", "    cal_profit_diff = simplify(cal_profit_diff)\n", "    result = solve(cal_profit_diff, amount_in)\n", "    if result:\n", "        print(\"func lens: \\n\", len(result))\n", "        func = result[1]\n", "        func0 = result[0]\n", "        print(\"func0: \\n\", func0)\n", "        print(\"func: \\n\", func)\n", "        print(\"cal_profit: \\n\", cal_profit)\n", "\n", "        def cal_max_profit_func(reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg,\n", "                                reserve_in_3_arg, reserve_out_3_arg, reserve_in_4_arg, reserve_out_4_arg):\n", "            return func.subs({\n", "                reserve_in_1: reserve_in_1_arg,\n", "                reserve_out_1: reserve_out_1_arg,\n", "                reserve_in_2: reserve_in_2_arg,\n", "                reserve_out_2: reserve_out_2_arg,\n", "                reserve_in_3: reserve_in_3_arg,\n", "                reserve_out_3: reserve_out_3_arg,\n", "                reserve_in_4: reserve_in_4_arg,\n", "                reserve_out_4: reserve_out_4_arg,\n", "            })\n", "\n", "        def cal_profit_func(amount_in_arg, reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg,\n", "                            reserve_in_3_arg, reserve_out_3_arg, reserve_in_4_arg, reserve_out_4_arg):\n", "            return cal_profit.subs({\n", "                amount_in: amount_in_arg,\n", "                reserve_in_1: reserve_in_1_arg,\n", "                reserve_out_1: reserve_out_1_arg,\n", "                reserve_in_2: reserve_in_2_arg,\n", "                reserve_out_2: reserve_out_2_arg,\n", "                reserve_in_3: reserve_in_3_arg,\n", "                reserve_out_3: reserve_out_3_arg,\n", "                reserve_in_4: reserve_in_4_arg,\n", "                reserve_out_4: reserve_out_4_arg,\n", "            })\n", "\n", "        return cal_max_profit_func, cal_profit_func\n"]}, {"cell_type": "code", "execution_count": 4, "id": "b519161d", "metadata": {}, "outputs": [], "source": ["def test_max_profit_4():\n", "    amount_in = 2.3 * 10 ** 18\n", "    # 套利路径 bnb -> cake -> usdc -> usdt ->bnb\n", "    reserve_in_1 = 50 * 10 ** 18\n", "    reserve_out_1 = 6685 * 10 ** 18\n", "    reserve_in_2 = 3500 * 10 ** 18\n", "    reserve_out_2 = 6800 * 10 ** 18\n", "    reserve_in_3 = 253433 * 10 ** 18\n", "    reserve_out_3 = 253433 * 10 ** 18\n", "    reserve_in_4 = 7553433 * 10 ** 18\n", "    reserve_out_4 = 31034 * 10 ** 18\n", "    f, cal_profit = get_cal_amount_in_to_max_profit_fd_inverse_4()\n", "    if not f:\n", "        return\n", "    max_amount_in = f(reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3, reserve_in_4, reserve_out_4)\n", "    max_profit = cal_profit(max_amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3,reserve_out_3, reserve_in_4, reserve_out_4)\n", "    print(f\"max_amount_in: {float(max_amount_in)}\\nmax_profit: {float(max_profit)}\")\n", "    profit = cal_profit(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3,\n", "                        reserve_out_3, reserve_in_4, reserve_out_4)\n", "    print(f\"amount_in: {amount_in}\\nprofit: {float(profit)}\")\n", "    amount_in = symbols(\"amount_in\")\n", "    plot(cal_profit(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3, reserve_in_4, reserve_out_4),\n", "         (amount_in, 0, 1.1*10**18))"]}, {"cell_type": "code", "execution_count": 5, "id": "37fb1581", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["func lens: \n", " 2\n", "func0: \n", " 1000000*(-1000000*reserve_in_1*reserve_in_2*reserve_in_3*reserve_in_4 - 994009*sqrt(reserve_in_1*reserve_in_2*reserve_in_3*reserve_in_4*reserve_out_1*reserve_out_2*reserve_out_3*reserve_out_4))/(997*(1000000000*reserve_in_2*reserve_in_3*reserve_in_4 + 997000000*reserve_in_3*reserve_in_4*reserve_out_1 + 994009000*reserve_in_4*reserve_out_1*reserve_out_2 + 991026973*reserve_out_1*reserve_out_2*reserve_out_3))\n", "func: \n", " 1000000*(-1000000*reserve_in_1*reserve_in_2*reserve_in_3*reserve_in_4 + 994009*sqrt(reserve_in_1*reserve_in_2*reserve_in_3*reserve_in_4*reserve_out_1*reserve_out_2*reserve_out_3*reserve_out_4))/(997*(1000000000*reserve_in_2*reserve_in_3*reserve_in_4 + 997000000*reserve_in_3*reserve_in_4*reserve_out_1 + 994009000*reserve_in_4*reserve_out_1*reserve_out_2 + 991026973*reserve_out_1*reserve_out_2*reserve_out_3))\n", "cal_profit: \n", " 988053892081*amount_in*reserve_out_1*reserve_out_2*reserve_out_3*reserve_out_4/((997*amount_in + 1000*reserve_in_1)*(994009*amount_in*reserve_out_1/(997*amount_in + 1000*reserve_in_1) + 1000*reserve_in_2)*(991026973*amount_in*reserve_out_1*reserve_out_2/((997*amount_in + 1000*reserve_in_1)*(994009*amount_in*reserve_out_1/(997*amount_in + 1000*reserve_in_1) + 1000*reserve_in_2)) + 1000*reserve_in_3)*(988053892081*amount_in*reserve_out_1*reserve_out_2*reserve_out_3/((997*amount_in + 1000*reserve_in_1)*(994009*amount_in*reserve_out_1/(997*amount_in + 1000*reserve_in_1) + 1000*reserve_in_2)*(991026973*amount_in*reserve_out_1*reserve_out_2/((997*amount_in + 1000*reserve_in_1)*(994009*amount_in*reserve_out_1/(997*amount_in + 1000*reserve_in_1) + 1000*reserve_in_2)) + 1000*reserve_in_3)) + 1000*reserve_in_4)) - amount_in\n", "max_amount_in: 4.560336230544045e+17\n", "max_profit: 1.226189984117816e+16\n", "amount_in: 2.2999999999999997e+18\n", "profit: -1.6427715819358182e+17\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["test_max_profit_4()"]}, {"cell_type": "code", "execution_count": 6, "id": "e2274e5d", "metadata": {}, "outputs": [], "source": ["def cal_amount_out5(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3, reserve_in_4, reserve_out_4, reserve_in_5, reserve_out_5):\n", "    amount_in_2, _ = cal_amount_out1(amount_in, reserve_in_1, reserve_out_1)\n", "    cal_amount_out_2, _ = cal_amount_out1(amount_in_2, reserve_in_2, reserve_out_2)\n", "    cal_amount_out_3, _ = cal_amount_out1(cal_amount_out_2, reserve_in_3, reserve_out_3)\n", "    cal_amount_out_4, _ = cal_amount_out1(cal_amount_out_3, reserve_in_4, reserve_out_4)\n", "    cal_amount_out_5, _ = cal_amount_out1(cal_amount_out_4, reserve_in_5, reserve_out_5)\n", "    cal_profit = cal_amount_out_5 - amount_in\n", "    return cal_amount_out_5, cal_profit\n", "\n", "\n", "def get_cal_amount_in_to_max_profit_fd_inverse_5():\n", "    amount_in = symbols('amount_in')\n", "    reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3, reserve_in_4, reserve_out_4, reserve_in_5, reserve_out_5  = symbols(\n", "        'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2 reserve_in_3 reserve_out_3 reserve_in_4 reserve_out_4 reserve_in_5 reserve_out_5')\n", "    cal_amount_out, cal_profit = cal_amount_out5(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2,\n", "                                                 reserve_in_3, reserve_out_3, reserve_in_4, reserve_out_4,  reserve_in_5, reserve_out_5)\n", "    # 计算 profit 斜率 随 amount_in 的变化，当 profit 斜率为0 时，profit 最大，计算出此时 amount_in 的值\n", "    cal_profit_diff = diff(cal_profit, amount_in)\n", "    cal_profit_diff = simplify(cal_profit_diff)\n", "    result = solve(cal_profit_diff, amount_in )\n", "    # result = solve(cal_profit_diff, amount_in, simplify=False, check=False)\n", "    if result:\n", "        print(\"func lens: \\n\", len(result))\n", "        func = result[1]\n", "        func0 = result[0]\n", "        print(\"func0: \\n\", func0)\n", "        print(\"func: \\n\", func)\n", "        print(\"cal_profit: \\n\", cal_profit)\n", "\n", "        def cal_max_profit_func(reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg,\n", "                                reserve_in_3_arg, reserve_out_3_arg, reserve_in_4_arg, reserve_out_4_arg, reserve_in_5_arg, reserve_out_5_arg):\n", "            return func.subs({\n", "                reserve_in_1: reserve_in_1_arg,\n", "                reserve_out_1: reserve_out_1_arg,\n", "                reserve_in_2: reserve_in_2_arg,\n", "                reserve_out_2: reserve_out_2_arg,\n", "                reserve_in_3: reserve_in_3_arg,\n", "                reserve_out_3: reserve_out_3_arg,\n", "                reserve_in_4: reserve_in_4_arg,\n", "                reserve_out_4: reserve_out_4_arg,\n", "                reserve_in_5: reserve_in_5_arg,\n", "                reserve_out_5: reserve_out_5_arg,\n", "            })\n", "\n", "        def cal_profit_func(amount_in_arg, reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg,\n", "                            reserve_in_3_arg, reserve_out_3_arg, reserve_in_4_arg, reserve_out_4_arg, reserve_in_5_arg, reserve_out_5_arg):\n", "            return cal_profit.subs({\n", "                amount_in: amount_in_arg,\n", "                reserve_in_1: reserve_in_1_arg,\n", "                reserve_out_1: reserve_out_1_arg,\n", "                reserve_in_2: reserve_in_2_arg,\n", "                reserve_out_2: reserve_out_2_arg,\n", "                reserve_in_3: reserve_in_3_arg,\n", "                reserve_out_3: reserve_out_3_arg,\n", "                reserve_in_4: reserve_in_4_arg,\n", "                reserve_out_4: reserve_out_4_arg,\n", "                reserve_in_5: reserve_in_5_arg,\n", "                reserve_out_5: reserve_out_5_arg,\n", "            })\n", "\n", "        return cal_max_profit_func, cal_profit_func\n"]}, {"cell_type": "code", "execution_count": 7, "id": "d9111cb4", "metadata": {}, "outputs": [], "source": ["def test_max_profit_5():\n", "    amount_in = 2.3 * 10 ** 18\n", "    # 套利路径 bnb -> cake -> usdc -> usdt -> dai ->bnb\n", "    reserve_in_1 = 50 * 10 ** 18\n", "    reserve_out_1 = 6685 * 10 ** 18\n", "    reserve_in_2 = 3500 * 10 ** 18\n", "    reserve_out_2 = 6800 * 10 ** 18\n", "    reserve_in_3 = 253433 * 10 ** 18\n", "    reserve_out_3 = 253433 * 10 ** 18\n", "    reserve_in_4 = 1553433 * 10 ** 18\n", "    reserve_out_4 = 1553433 * 10 ** 18\n", "    reserve_in_5 = 7553433 * 10 ** 18\n", "    reserve_out_5 = 31034 * 10 ** 18\n", "    f, cal_profit = get_cal_amount_in_to_max_profit_fd_inverse_5()\n", "    if not f:\n", "        return\n", "    max_amount_in = f(reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3, reserve_in_4, reserve_out_4, reserve_in_5, reserve_out_5)\n", "    max_profit = cal_profit(max_amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3,reserve_out_3, reserve_in_4, reserve_out_4)\n", "    print(f\"max_amount_in: {float(max_amount_in)}\\nmax_profit: {float(max_profit)}\")\n", "    profit = cal_profit(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3,\n", "                        reserve_out_3, reserve_in_4, reserve_out_4, reserve_in_5, reserve_out_5)\n", "    print(f\"amount_in: {amount_in}\\nprofit: {float(profit)}\")\n", "    amount_in = symbols(\"amount_in\")\n", "    plot(cal_profit(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2, reserve_in_3, reserve_out_3, reserve_in_4, reserve_out_4, reserve_in_5, reserve_out_5),\n", "         (amount_in, 0, 1.1*10**18))"]}, {"cell_type": "code", "execution_count": 8, "id": "cab51baf", "metadata": {}, "outputs": [], "source": ["test_max_profit_5()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 5}