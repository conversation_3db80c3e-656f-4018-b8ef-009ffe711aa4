{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["def cross_reserve_target_amount(source_reserve, source_weight, target_reserve, target_weight, source_amount) -> int:\n", "    '''\n", "    return = _targetReserveBalance * (1 - (_sourceReserveBalance / (_sourceReserveBalance + _amount)) ^ (_sourceReserveWeight / _targetReserveWeight))\n", "    '''\n", "    if source_weight == target_weight:\n", "        return target_reserve * source_amount / (source_reserve + source_amount)\n", "    result = target_reserve * (1 - (source_reserve / (source_reserve + source_amount)) ** (source_weight / target_weight))\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = cross_reserve_target_amount(100, 100, 100, 100, 100)\n", "print(result)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}