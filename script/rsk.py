def cross_reserve_target_amount(source_reserve, source_weight, target_reserve, target_weight, source_amount) -> int:
    '''
    return = _targetReserveBalance * (1 - (_sourceReserveBalance / (_sourceReserveBalance + _amount)) ^ (_sourceReserveWeight / _targetReserveWeight))
    '''
    if source_weight == target_weight:
        return target_reserve * source_amount / (source_reserve + source_amount)
    result = target_reserve * (1 - (source_reserve / (source_reserve + source_amount)) ** (source_weight / target_weight))
    return result

if __name__ == "__main__":
    pass