{"cells": [{"cell_type": "code", "execution_count": 185, "id": "10c036a2-6e82-44c5-88b9-b6373a43fed1", "metadata": {}, "outputs": [], "source": ["from sympy import diff, solve, symbols\n", "from sympy.abc import x\n", "from sympy.plotting import plot\n", "\n", "BigInt = int\n", "\n", "\n", "def gen_get_amount_out(reserve_in: BigInt, reserve_out: BigInt):\n", "    def f(amount_in: BigInt):\n", "        amount_in_with_fee = amount_in * 997\n", "        numerator = amount_in_with_fee * reserve_out\n", "        denominator = reserve_in * 1000 + amount_in_with_fee\n", "        amount_out = numerator / denominator\n", "        return amount_out\n", "\n", "    return f\n", "\n", "\n", "def gen_cal_profit(reserve_list):\n", "    def cal_profit(amount_in):\n", "        amount_out = amount_in\n", "        for reserve_in, reserve_out in reserve_list:\n", "            f = gen_get_amount_out(reserve_in, reserve_out)\n", "            amount_out = f(amount_out)\n", "        return amount_out - amount_in\n", "\n", "    return cal_profit\n", "\n", "\n", "def plot_k(reserve_list):\n", "    # (x + delta_x) * (y - delta_y) = xy\n", "    # delta_y = y - xy / (x + delta_x)\n", "    p_k = plot(xlim=(0, 2), ylim=(0, 2))\n", "    for reserve_in, reserve_out in reserve_list:\n", "        f = reserve_in * reserve_out / x\n", "        p = plot(f, show=False, axis_center=(0, 0))\n", "        p_k.extend(p)\n", "    p_k.show()\n", "\n", "\n", "def plot_amount_in(reserve_list):\n", "    # (x + delta_x) * (y - delta_y) = xy\n", "    # delta_y = y - xy / (x + delta_x)\n", "    p_amount_in = plot()\n", "    for reserve_in, reserve_out in reserve_list:\n", "        f = reserve_out - reserve_in * reserve_out / (x + reserve_in)\n", "        p = plot(f, show=False, axis_center=(0, 0))\n", "        p_amount_in.extend(p)\n", "    p_amount_in.show()\n", "\n", "\n", "def cal_amount_in_to_max_profit(reserve_list, with_plot=False):\n", "    cal_profit = gen_cal_profit(reserve_list)\n", "    cal_profit_f = diff(cal_profit(x), x)  # 利润函数的导数\n", "    if with_plot:\n", "        # p1 = plot(cal_profit_f, show=False, axis_center=(0, 0), label=\"profit_diff\", xlim=(-10000, 100000))\n", "        p2 = plot(cal_profit(x), show=False, axis_center=(0, 0), label=\"profit\", xlim=(-10000, 100000))\n", "        # p1.extend(p2)\n", "        # p1.show()\n", "        p2.show()\n", "    result = solve(cal_profit_f, x)\n", "    amount_in = int(result[0])\n", "    profit = cal_profit(amount_in)\n", "    return amount_in, profit\n", "\n", "\n", "def test_cal():\n", "    # f1 = gen_get_amount_out(Decimal(1030), Decimal(2000))\n", "    # f2 = gen_get_amount_out(<PERSON><PERSON><PERSON>(2010), Dec<PERSON><PERSON>(1888))\n", "    # f3 = gen_get_amount_out(Decimal(2111), Decimal(1543))\n", "    # reserve_list = [\n", "    #     [<PERSON><PERSON><PERSON>(2111), <PERSON><PERSON><PERSON>(1543)],\n", "    #     [<PERSON><PERSON><PERSON>(2010), <PERSON><PERSON><PERSON>(1888)],\n", "    #     [<PERSON><PERSON><PERSON>(1030), <PERSON><PERSON><PERSON>(2000)],\n", "    # ]\n", "    # 假设套路路径 bnb -> cake -> usdt -> bnb\n", "    reserve_list = [\n", "        [BigInt(50 * 10 ** 18), BigInt(6685 * 10 ** 18)],  # bnb/cake\n", "        [BigInt(20000 * 10 ** 18), BigInt(32880 * 10 ** 18)],  # cake/usdt\n", "        [BigInt(24300 * 10 ** 18), BigInt(115 * 10 ** 18)],  # usdt/bnb\n", "    ]\n", "    # plot_k(reserve_list)\n", "    print(\"max_profit\", cal_amount_in_to_max_profit(reserve_list))\n", "\n", "\n", "def cal_amount_out1(amount_in: any, reserve_in, reserve_out):\n", "    amount_in_with_fee = amount_in * 997\n", "    numerator = amount_in_with_fee * reserve_out\n", "    denominator = reserve_in * 1000 + amount_in_with_fee\n", "    cal_amount_out_1 = numerator / denominator\n", "    cal_profit_1 = cal_amount_out_1 - amount_in\n", "    return cal_amount_out_1, cal_profit_1\n", "\n", "\n", "def cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2):\n", "    amount_in_2, _ = cal_amount_out1(amount_in, reserve_in_1, reserve_out_1)\n", "    cal_amount_out_2, _ = cal_amount_out1(amount_in_2, reserve_in_2, reserve_out_2)\n", "    cal_profit_2 = cal_amount_out_2 - amount_in\n", "    return cal_amount_out_2, cal_profit_2\n", "\n", "\n", "def cal_amount_in_to_max_profit_fd_1(amount_in, reserve_in, reserve_out):\n", "    cal_amount_out, cal_profit = cal_amount_out1(amount_in, reserve_in, reserve_out)\n", "    cal_profit_diff = diff(cal_profit, amount_in)\n", "    return cal_profit_diff\n", "\n", "\n", "def cal_amount_in_to_max_profit_fd_2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2):\n", "    cal_amount_out, cal_profit = cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    cal_profit_diff = diff(cal_profit, amount_in)\n", "    return cal_profit_diff\n", "\n", "\n", "def get_cal_amount_in_to_max_profit_fd_2():\n", "    amount_in = symbols('amount_in')\n", "    reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2 = symbols(\n", "        'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2')\n", "    cal_amount_out, cal_profit = cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    cal_profit_diff = diff(cal_profit, amount_in)\n", "\n", "    def f(amount_in_arg, reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg):\n", "        return cal_profit_diff.subs({\n", "            amount_in: amount_in_arg,\n", "            reserve_in_1: reserve_in_1_arg,\n", "            reserve_out_1: reserve_out_1_arg,\n", "            reserve_in_2: reserve_in_2_arg,\n", "            reserve_out_2: reserve_out_2_arg,\n", "        })\n", "\n", "    def cal_profit_func(amount_in_arg, reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg):\n", "        return cal_profit.subs({\n", "            amount_in: amount_in_arg,\n", "            reserve_in_1: reserve_in_1_arg,\n", "            reserve_out_1: reserve_out_1_arg,\n", "            reserve_in_2: reserve_in_2_arg,\n", "            reserve_out_2: reserve_out_2_arg,\n", "        })\n", "\n", "    return f, cal_profit_func\n", "\n", "\n", "def get_cal_amount_in_to_max_profit_fd_inverse_2():\n", "    amount_in = symbols('amount_in')\n", "    reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2 = symbols(\n", "        'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2')\n", "    cal_amount_out, cal_profit = cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    # 计算 profit 斜率 随 amount_in 的变化，当 profit 斜率为0 时，profit 最大，计算出此时 amount_in 的值\n", "    cal_profit_diff = diff(cal_profit, amount_in)\n", "    result = solve(cal_profit_diff, amount_in)\n", "    if result:\n", "        func = result[0]\n", "        print(\"func: \\n\", func)\n", "        print(\"cal_profit: \\n\", cal_profit)\n", "\n", "        def cal_max_profit_func(reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg):\n", "            return func.subs({\n", "                reserve_in_1: reserve_in_1_arg,\n", "                reserve_out_1: reserve_out_1_arg,\n", "                reserve_in_2: reserve_in_2_arg,\n", "                reserve_out_2: reserve_out_2_arg,\n", "            })\n", "\n", "        def cal_profit_func(amount_in_arg, reserve_in_1_arg, reserve_out_1_arg, reserve_in_2_arg, reserve_out_2_arg):\n", "            return cal_profit.subs({\n", "                amount_in: amount_in_arg,\n", "                reserve_in_1: reserve_in_1_arg,\n", "                reserve_out_1: reserve_out_1_arg,\n", "                reserve_in_2: reserve_in_2_arg,\n", "                reserve_out_2: reserve_out_2_arg,\n", "            })\n", "\n", "        return cal_max_profit_func, cal_profit_func\n", "\n", "\n", "def test_max_profit():\n", "    amount_in = 1 * 10 ** 18\n", "    # 套利路径 bnb -> cake -> bnb\n", "    reserve_in_1 = 50 * 10 ** 18\n", "    reserve_out_1 = 6685 * 10 ** 18\n", "    reserve_in_2 = 20000 * 10 ** 18\n", "    reserve_out_2 = 169 * 10 ** 18\n", "    f, cal_profit = get_cal_amount_in_to_max_profit_fd_inverse_2()\n", "    if not f:\n", "        return\n", "    max_amount_in = f(reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    max_profit = cal_profit(max_amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    print(f\"max_amount_in: {float(max_amount_in)}\\nmax_profit: {float(max_profit)}\")\n", "    profit = cal_profit(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    print(f\"amount_in: {amount_in}\\nprofit: {float(profit)}\")\n", "    plot(cal_profit(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2),\n", "         (amount_in, 0, 100000000000000000000))\n", "\n", "\n", "def test_fd2():\n", "    amount_in = 1 * 10 ** 18\n", "    # 套利路径 bnb -> cake -> bnb\n", "    reserve_in_1 = 50 * 10 ** 18\n", "    reserve_out_1 = 6685 * 10 ** 18\n", "    reserve_in_2 = 20000 * 10 ** 18\n", "    reserve_out_2 = 149 * 10 ** 18\n", "    f, cal_profit_func = get_cal_amount_in_to_max_profit_fd_2()\n", "    ratio = f(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    print(\"ratio: \\n\", ratio)\n", "    print(\"profit: \\n\", cal_profit_func(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2))\n", "\n", "\n", "def cal_profit_plot():\n", "    amount_in = symbols('amount_in')\n", "\n", "    # reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2 = symbols(\n", "    #     'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2')\n", "    reserve_in_1 = 50 * 10 ** 18\n", "    reserve_out_1 = 6685 * 10 ** 18\n", "    reserve_in_2 = 20000 * 10 ** 18\n", "    reserve_out_2 = 169 * 10 ** 18\n", "\n", "    # reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2 = symbols(\n", "    #     'reserve_in_1 reserve_out_1 reserve_in_2 reserve_out_2')\n", "    cal_amount_out, cal_profit = cal_amount_out2(amount_in, reserve_in_1, reserve_out_1, reserve_in_2, reserve_out_2)\n", "    print(\"start profit plot\")\n", "    print(cal_profit)\n", "    p1 = plot(cal_amount_out, (amount_in, 0, 160 * 10 ** 18), ylim=(0, 50 * 10 ** 18), show=False, ylabel=\"amount_out\")\n", "    p2 = plot(amount_in, (amount_in, 0, 160 * 10 ** 18), show=False)\n", "    p1.extend(p2)\n", "    p1.show()\n", "    plot(cal_profit, (amount_in, 0, 10 ** 19), ylabel=\"profit\")\n", "    print(\"end profit plot\")\n", "\n"]}, {"cell_type": "code", "execution_count": 186, "id": "ea70c77d-3083-4fcb-bfc4-2bc23128e173", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["start profit plot\n", "-amount_in + 1122996577885000000000000000000000000000000000000*amount_in/((997*amount_in + 50000000000000000000000)*(6644950165000000000000000000*amount_in/(997*amount_in + 50000000000000000000000) + 20000000000000000000000000))\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["end profit plot\n"]}], "source": ["cal_profit_plot()"]}, {"cell_type": "code", "execution_count": null, "id": "b3d361e0-1e8e-46d7-bb3d-35c237f86d85", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}