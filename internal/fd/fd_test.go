package fd

import (
	"fmt"
	"testing"

	"github.com/shopspring/decimal"
)

func TestCalMaxProfit2(t *testing.T) {
	// 套利路径 bnb -> cake -> bnb
	amountIn := decimal.New(1, 0)
	reserveIn1 := decimal.New(50, 18)
	reserveOut1 := decimal.New(6685, 18)
	reserveIn2 := decimal.New(20000, 18)
	reserveOut2 := decimal.New(169, 18)
	profit := CalProfit2(amountIn, reserveIn1, reserveOut1, reserveIn2, reserveOut2)
	fmt.Println("profit:", profit.String())
	maxAmountIn := CalAmountInToMaxProfit2(reserveIn1, reserveOut1, reserveIn2, reserveOut2)
	fmt.Println("max amount in:", maxAmountIn.String())
	maxProfit := CalProfit2(maxAmountIn, reserveIn1, reserveOut1, reserveIn2, reserveOut2)
	fmt.Println("max profit:", maxProfit.String())
	exceptProfit, _ := decimal.NewFromString("1.3413305121131628e+17")
	if maxProfit.Sub(exceptProfit).GreaterThan(decimal.New(1, 0)) {
		fmt.Printf("max profit: %s\n", maxProfit.String())
		fmt.Printf("except profit: %s\n", exceptProfit.String())
		t.Error("max profit error")
	}
	exceptAmountIn, _ := decimal.NewFromString("2.246208036159915889e+18")
	if maxAmountIn.Sub(exceptAmountIn).GreaterThan(decimal.New(1, 0)) {
		fmt.Printf("max amount in: %s\n", maxAmountIn.String())
		fmt.Printf("except amount in: %s\n", exceptAmountIn.String())
		t.Error("max amount in error")
	}
}

func TestCalMaxProfit3(t *testing.T) {
	//reserve_in_1 = 50 * 10 ** 18
	//reserve_out_1 = 6685 * 10 ** 18
	//reserve_in_2 = 3500 * 10 ** 18
	//reserve_out_2 = 6800 * 10 ** 18
	//reserve_in_3 = 253433 * 10 ** 18
	//reserve_out_3 = 1034 * 10 ** 18

	reserveIn1 := decimal.New(50, 18)
	reserveOut1 := decimal.New(6685, 18)
	reserveIn2 := decimal.New(3500, 18)
	reserveOut2 := decimal.New(6800, 18)
	reserveIn3 := decimal.New(253433, 18)
	reserveOut3 := decimal.New(1034, 18)
	maxAmountIn := CalAmountInToMaxProfit3(reserveIn1, reserveOut1, reserveIn2, reserveOut2, reserveIn3, reserveOut3)
	fmt.Println("max amount in:", maxAmountIn.String())
	maxProfit := CalProfit3(maxAmountIn, reserveIn1, reserveOut1, reserveIn2, reserveOut2, reserveIn3, reserveOut3)
	fmt.Println("max profit:", maxProfit.String())
}

func TestCalMaxProfit4(t *testing.T) {
	reserveIn1 := decimal.New(50, 18)
	reserveOut1 := decimal.New(6685, 18)
	reserveIn2 := decimal.New(3500, 18)
	reserveOut2 := decimal.New(6800, 18)
	reserveIn3 := decimal.New(253433, 18)
	reserveOut3 := decimal.New(253433, 18)
	reserveIn4 := decimal.New(7553433, 18)
	reserveOut4 := decimal.New(31034, 18)
	maxAmountIn := CalAmountInToMaxProfit4(reserveIn1, reserveOut1, reserveIn2, reserveOut2, reserveIn3, reserveOut3, reserveIn4, reserveOut4)
	fmt.Println("max amount in:", maxAmountIn.String())
	maxProfit := CalProfit4(maxAmountIn, reserveIn1, reserveOut1, reserveIn2, reserveOut2, reserveIn3, reserveOut3, reserveIn4, reserveOut4)
	fmt.Println("max profit:", maxProfit.String())
	// 1.226189984117816e+16
	// 12261899841178159
}

func TestCalAmountInAmountOut(t *testing.T) {
	reserveIn1, _ := decimal.NewFromString("1000515215022097818")
	reserveOut1, _ := decimal.NewFromString("99960400000000000000000")
	reserveIn2, _ := decimal.NewFromString("4988120000000000000000")
	reserveOut2, _ := decimal.NewFromString("1002736388328466179")
	reserveIns := []decimal.Decimal{
		reserveIn1, reserveIn2,
	}
	reserveOuts := []decimal.Decimal{
		reserveOut1, reserveOut2,
	}
	amountIn, amountOuts := CalAmountInAmountOut(reserveIns, reserveOuts)
	fmt.Println("amount in:", amountIn.String())
	fmt.Println("amount out:", amountOuts[len(amountOuts)-1].String())
}

func TestCalAmountOut(t *testing.T) {
	// 969843392100783918830349
	// 969843392100783918830349
	// 15887624300896585890147
	// 15887624300896585890147,
	amountIn, _ := decimal.NewFromString("363678116953075474")
	reserve0, _ := decimal.NewFromString("15887624300896585890147")
	reserve1, _ := decimal.NewFromString("969843392100783918830349")
	result := CalAmountOut1(amountIn, reserve0, reserve1)
	// 22133243726989735798
	// 22133243726989735797
	fmt.Println(result.RoundDown(0))
	fmt.Println(result.BigInt())
}
