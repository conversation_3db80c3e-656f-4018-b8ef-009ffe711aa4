package fd

import (
	"math/big"

	"github.com/shopspring/decimal"
)

var (
	d1               = decimal.NewFromInt(1)
	d2               = decimal.NewFromInt(2)
	d997             = decimal.NewFromInt(997)
	d1000            = decimal.NewFromInt(1000)
	d10000           = decimal.NewFromInt(10000)
	d100000          = decimal.NewFromInt(100000)
	d994009          = decimal.NewFromInt(994009)
	d997000          = decimal.NewFromInt(997000)
	d1000000         = decimal.NewFromInt(1000000)
	d991026973       = decimal.NewFromInt(991026973)
	d994009000       = decimal.NewFromInt(994009000)
	d997000000       = decimal.NewFromInt(997000000)
	d1000000000      = decimal.NewFromInt(1000000000)
	d10000000000     = decimal.NewFromInt(10000000000)
	d988053892081000 = decimal.NewFromInt(988053892081000)

	// sqrt(9970) = sqrt(9970 * 10 ** 30) / sqrt(10 ** 30)
	sqrtd9970, _ = decimal.NewFromString("99.849887330932927798322882923392861169923717415662")
)

// CalAmountOut1 单层套利路径
func CalAmountOut1(amountIn, reserveIn, reserveOut decimal.Decimal) decimal.Decimal {
	amountInWithFee := amountIn.Mul(decimal.NewFromInt(997))
	numerator := amountInWithFee.Mul(reserveOut)
	denominator := reserveIn.Mul(decimal.NewFromInt(1000)).Add(amountInWithFee)
	if !denominator.IsPositive() {
		return decimal.Zero
	}
	amountOut := numerator.Div(denominator)
	return amountOut.Sub(d1)
}

// CalAmountIn1 单层套利路径
func CalAmountIn1(amountOut, reserveIn, reserveOut decimal.Decimal) decimal.Decimal {
	numerator := reserveIn.Mul(amountOut).Mul(d1000)
	denominator := reserveOut.Sub(amountOut).Mul(d997)
	if !denominator.IsPositive() {
		return decimal.Zero
	}
	amountIn := numerator.Div(denominator)
	return amountIn.Add(d1)
}

// CalProfitDerivative1 单层套利路径导数
func CalProfitDerivative1(amountIn, reserveIn, reserveOut decimal.Decimal) decimal.Decimal {
	// 994009*amount_in*reserve_out/(997*amount_in + 1000*reserve_in)**2 - 997*reserve_out/(997*amount_in + 1000*reserve_in) + 1
	return d994009.Mul(amountIn).Mul(reserveOut).Div(
		d997.Mul(amountIn).Add(d1000.Mul(reserveIn)).Pow(d2),
	).Sub(d997.Mul(reserveOut).Div(d997.Mul(amountIn).Add(d1000.Mul(reserveIn)))).Add(d1)
}

func CalAmountOut2(amountIn decimal.Decimal, reserveIn1, reserveOut1, reserveIn2, reserveOut2 decimal.Decimal) []decimal.Decimal {
	amountOut1 := CalAmountOut1(amountIn, reserveIn1, reserveOut1)
	amountOut2 := CalAmountOut1(amountOut1, reserveIn2, reserveOut2)
	return []decimal.Decimal{amountOut1, amountOut2}
}

// CalProfitDerivative2 双层套利路径导数
func CalProfitDerivative2(amountIn decimal.Decimal, reserveIn1, reserveOut1, reserveIn2, reserveOut2 decimal.Decimal) decimal.Decimal {
	// (988053892081000*amount_in*reserve_in_1*reserve_out_1**2*reserve_out_2 + 991026973*amount_in*reserve_out_1*reserve_out_2*(994009*amount_in*reserve_out_1 + 1000*reserve_in_2*(997*amount_in + 1000*reserve_in_1)) - 994009*reserve_out_1*reserve_out_2*(997*amount_in + 1000*reserve_in_1)*(994009*amount_in*reserve_out_1 + 1000*reserve_in_2*(997*amount_in + 1000*reserve_in_1)) + (997*amount_in + 1000*reserve_in_1)*(994009*amount_in*reserve_out_1 + 1000*reserve_in_2*(997*amount_in + 1000*reserve_in_1))**2)/((997*amount_in + 1000*reserve_in_1)*(994009*amount_in*reserve_out_1 + 1000*reserve_in_2*(997*amount_in + 1000*reserve_in_1))**2)
	// a1 = 988053892081000*amount_in*reserve_in_1*reserve_out_1**2*reserve_out_2
	// a2 = 991026973*amount_in*reserve_out_1*reserve_out_2*(994009*amount_in*reserve_out_1 + 1000*reserve_in_2*(997*amount_in + 1000*reserve_in_1))
	// a3 = 994009*reserve_out_1*reserve_out_2*(997*amount_in + 1000*reserve_in_1)*(994009*amount_in*reserve_out_1 + 1000*reserve_in_2*(997*amount_in + 1000*reserve_in_1))
	// a4 = (997*amount_in + 1000*reserve_in_1)*(994009*amount_in*reserve_out_1 + 1000*reserve_in_2*(997*amount_in + 1000*reserve_in_1))**2
	// a5 = ((997*amount_in + 1000*reserve_in_1)*(994009*amount_in*reserve_out_1 + 1000*reserve_in_2*(997*amount_in + 1000*reserve_in_1))**2)
	// (a1 + a2 - a3 + a4) / a5
	a1 := d988053892081000.Mul(amountIn).Mul(reserveIn1).Mul(reserveOut1.Pow(d2)).Mul(reserveOut2)
	a2 := d991026973.Mul(amountIn).Mul(reserveOut1).Mul(reserveOut2).Mul(d994009.Mul(amountIn).Mul(reserveOut1).Add(d1000.Mul(reserveIn2).Mul(d997.Mul(amountIn).Add(d1000.Mul(reserveIn1)))))
	a3 := d994009.Mul(reserveOut1).Mul(reserveOut2).Mul(d997.Mul(amountIn).Add(d1000.Mul(reserveIn1))).Mul(d994009.Mul(amountIn).Mul(reserveOut1).Add(d1000.Mul(reserveIn2).Mul(d997.Mul(amountIn).Add(d1000.Mul(reserveIn1)))))
	a4 := d997.Mul(amountIn).Add(d1000.Mul(reserveIn1)).Mul(d994009.Mul(amountIn).Mul(reserveOut1).Add(d1000.Mul(reserveIn2).Mul(d997.Mul(amountIn).Add(d1000.Mul(reserveIn1)))).Pow(d2))
	a5 := a4
	// a5 != 0，否则无解
	return a1.Add(a2).Sub(a3).Add(a4).Div(a5)
}

func CalAmountOut3(amountIn decimal.Decimal, reserveIn1, reserveOut1, reserveIn2, reserveOut2, reserveIn3, reserveOut3 decimal.Decimal) []decimal.Decimal {
	amountOut1 := CalAmountOut1(amountIn, reserveIn1, reserveOut1)
	amountOut2 := CalAmountOut1(amountOut1, reserveIn2, reserveOut2)
	amountOut3 := CalAmountOut1(amountOut2, reserveIn3, reserveOut3)
	return []decimal.Decimal{amountOut1, amountOut2, amountOut3}
}

func CalAmountOut4(amountIn decimal.Decimal, reserveIn1, reserveOut1, reserveIn2, reserveOut2, reserveIn3, reserveOut3, reserveIn4, reserveOut4 decimal.Decimal) []decimal.Decimal {
	amountOut1 := CalAmountOut1(amountIn, reserveIn1, reserveOut1)
	amountOut2 := CalAmountOut1(amountOut1, reserveIn2, reserveOut2)
	amountOut3 := CalAmountOut1(amountOut2, reserveIn3, reserveOut3)
	amountOut4 := CalAmountOut1(amountOut3, reserveIn4, reserveOut4)
	return []decimal.Decimal{amountOut1, amountOut2, amountOut3, amountOut4}
}

// CalAmountInToMaxProfit2 2层套利路径最大收益
func CalAmountInToMaxProfit2(reserveIn1, reserveOut1, reserveIn2, reserveOut2 decimal.Decimal) decimal.Decimal {
	// 有两个解，取第二个解，第一个解是负数
	// (-1000000*reserve_in_1*reserve_in_2 - 997000*sqrt(reserve_in_1*reserve_in_2*reserve_out_1*reserve_out_2))/(997000*reserve_in_2 + 994009*reserve_out_1)
	// (-1000000*reserve_in_1*reserve_in_2 + 997000*sqrt(reserve_in_1*reserve_in_2*reserve_out_1*reserve_out_2))/(997000*reserve_in_2 + 994009*reserve_out_1)
	a1 := d1000000.Neg().Mul(reserveIn1).Mul(reserveIn2)
	a2 := reserveIn1.Mul(reserveIn2).Mul(reserveOut1).Mul(reserveOut2)
	a2 = decimal.NewFromBigInt(new(big.Int).Sqrt(a2.BigInt()), 0)
	a3 := d997000.Mul(a2)
	numerator := a1.Add(a3)
	if !numerator.IsPositive() {
		return decimal.Zero
	}
	a4 := d997000.Mul(reserveIn2).Add(d994009.Mul(reserveOut1))
	r2 := numerator.Div(a4)
	return r2
	// r1 := a1.Sub(a3).Div(a4)
	// return decimal.Max(r1, r2)
}

// CalAmountInToMaxProfit3 3层套利路径最大收益
func CalAmountInToMaxProfit3(reserveIn1, reserveOut1, reserveIn2, reserveOut2, reserveIn3, reserveOut3 decimal.Decimal) decimal.Decimal {
	// 10000*(-100000*reserve_in_1*reserve_in_2*reserve_in_3 - 997*sqrt(9970)*sqrt(reserve_in_1*reserve_in_2*reserve_in_3*reserve_out_1*reserve_out_2*reserve_out_3)) /
	//  (997*(1000000*reserve_in_2*reserve_in_3 + 997000*reserve_in_3*reserve_out_1 + 994009*reserve_out_1*reserve_out_2))
	// 10000*(-100000*reserve_in_1*reserve_in_2*reserve_in_3 + 997*sqrt(9970)*sqrt(reserve_in_1*reserve_in_2*reserve_in_3*reserve_out_1*reserve_out_2*reserve_out_3))/
	//  (997*(1000000*reserve_in_2*reserve_in_3 + 997000*reserve_in_3*reserve_out_1 + 994009*reserve_out_1*reserve_out_2))
	// a1 = -100000*reserve_in_1*reserve_in_2*reserve_in_3
	// a2 = reserve_in_1*reserve_in_2*reserve_in_3*reserve_out_1*reserve_out_2*reserve_out_3
	// a3 = sqrt(a2)
	// a4 = 997*sqrt(9970)*a3
	// a5 = 1000000*reserve_in_2*reserve_in_3 + 997000*reserve_in_3*reserve_out_1 + 994009*reserve_out_1*reserve_out_2
	a1 := d100000.Neg().Mul(reserveIn1).Mul(reserveIn2).Mul(reserveIn3)
	a2 := reserveIn1.Mul(reserveIn2).Mul(reserveIn3).Mul(reserveOut1).Mul(reserveOut2).Mul(reserveOut3)
	a3 := decimal.NewFromBigInt(new(big.Int).Sqrt(a2.BigInt()), 0)
	a4 := d997.Mul(sqrtd9970).Mul(a3)
	//r1 := d10000.Mul(a1.Sub(a4)).Div(a5)
	numerator := a1.Add(a4)
	if !numerator.IsPositive() {
		return decimal.Zero
	}
	a5 := d997.Mul(d1000000.Mul(reserveIn2).Mul(reserveIn3).Add(d997000.Mul(reserveIn3).Mul(reserveOut1)).Add(d994009.Mul(reserveOut1).Mul(reserveOut2)))
	r2 := d10000.Mul(a1.Add(a4)).Div(a5)
	//return decimal.Max(r1, r2)
	return r2
}

func CalAmountInToMaxProfit4(reserveIn1, reserveOut1, reserveIn2, reserveOut2, reserveIn3, reserveOut3, reserveIn4, reserveOut4 decimal.Decimal) decimal.Decimal {
	// 第一个解
	// 1000000*(-1000000*reserve_in_1*reserve_in_2*reserve_in_3*reserve_in_4 - 994009*sqrt(reserve_in_1*reserve_in_2*reserve_in_3*reserve_in_4*reserve_out_1*reserve_out_2*reserve_out_3*reserve_out_4))/(997*(1000000000*reserve_in_2*reserve_in_3*reserve_in_4 + 997000000*reserve_in_3*reserve_in_4*reserve_out_1 + 994009000*reserve_in_4*reserve_out_1*reserve_out_2 + 991026973*reserve_out_1*reserve_out_2*reserve_out_3))
	// 第二个解
	// 1000000*(-1000000*reserve_in_1*reserve_in_2*reserve_in_3*reserve_in_4 + 994009*sqrt(reserve_in_1*reserve_in_2*reserve_in_3*reserve_in_4*reserve_out_1*reserve_out_2*reserve_out_3*reserve_out_4))/(997*(1000000000*reserve_in_2*reserve_in_3*reserve_in_4 + 997000000*reserve_in_3*reserve_in_4*reserve_out_1 + 994009000*reserve_in_4*reserve_out_1*reserve_out_2 + 991026973*reserve_out_1*reserve_out_2*reserve_out_3))
	commonExpression := reserveIn1.Mul(reserveIn2).Mul(reserveIn3).Mul(reserveIn4)
	expression1 := commonExpression.Mul(d1000000)
	expression2 := decimal.NewFromBigInt(new(big.Int).Sqrt(commonExpression.Mul(reserveOut1).Mul(reserveOut2).Mul(reserveOut3).Mul(reserveOut4).Mul(d10000000000).BigInt()), 0).Div(d100000)
	expression3 := d1000000000.Mul(reserveIn2).Mul(reserveIn3).Mul(reserveIn4)
	expression4 := d997000000.Mul(reserveIn3).Mul(reserveIn4).Mul(reserveOut1)
	expression5 := d994009000.Mul(reserveIn4).Mul(reserveOut1).Mul(reserveOut2)
	expression6 := d991026973.Mul(reserveOut1).Mul(reserveOut2).Mul(reserveOut3)

	a1 := expression2.Mul(d994009).Sub(expression1)
	// 是负数则不计算
	if !a1.IsPositive() {
		return decimal.Zero
	}
	// 可能是负数
	numerator := a1.Mul(d1000000)
	// 一定是正数
	denominator := expression3.Add(expression4).Add(expression5).Add(expression6).Mul(d997)
	return numerator.Div(denominator)
}

func CalAmountInToMaxProfit(reserveIns, reserveOuts []decimal.Decimal) decimal.Decimal {
	if len(reserveIns) != len(reserveOuts) {
		return decimal.Zero
	}
	switch len(reserveIns) {
	case 2:
		return CalAmountInToMaxProfit2(reserveIns[0], reserveOuts[0], reserveIns[1], reserveOuts[1])
	case 3:
		return CalAmountInToMaxProfit3(reserveIns[0], reserveOuts[0], reserveIns[1], reserveOuts[1], reserveIns[2], reserveOuts[2])
	case 4:
		return CalAmountInToMaxProfit4(reserveIns[0], reserveOuts[0], reserveIns[1], reserveOuts[1], reserveIns[2], reserveOuts[2], reserveIns[3], reserveOuts[3])
	}
	return decimal.Zero
}

func CalAmountInAmountOut(reserveIns, reserveOuts []decimal.Decimal) (decimal.Decimal, []decimal.Decimal) {
	amountIn := CalAmountInToMaxProfit(reserveIns, reserveOuts)
	amountOut := []decimal.Decimal{decimal.Zero}
	// amount in 如果是负数，不需要计算 amount_out
	if amountIn.IsPositive() {
		amountOut = CalAmountOut(amountIn, reserveIns, reserveOuts)
	}
	return amountIn, amountOut
}

func CalProfit4(amountIn, reserveIn1, reserveOut1, reserveIn2, reserveOut2, reserveIn3, reserveOut3, reserveIn4, reserveOut4 decimal.Decimal) decimal.Decimal {
	amountOut1 := CalAmountOut1(amountIn, reserveIn1, reserveOut1)
	amountOut2 := CalAmountOut1(amountOut1, reserveIn2, reserveOut2)
	amountOut3 := CalAmountOut1(amountOut2, reserveIn3, reserveOut3)
	amountOut4 := CalAmountOut1(amountOut3, reserveIn4, reserveOut4)
	return amountOut4.Sub(amountIn)
}

func CalProfit3(amountIn, reserveIn1, reserveOut1, reserveIn2, reserveOut2, reserveIn3, reserveOut3 decimal.Decimal) decimal.Decimal {
	amountOut1 := CalAmountOut1(amountIn, reserveIn1, reserveOut1)
	amountOut2 := CalAmountOut1(amountOut1, reserveIn2, reserveOut2)
	amountOut3 := CalAmountOut1(amountOut2, reserveIn3, reserveOut3)
	return amountOut3.Sub(amountIn)
}

// CalProfit2 2层套利路径收益
func CalProfit2(amountIn decimal.Decimal, reserveIn1, reserveOut1, reserveIn2, reserveOut2 decimal.Decimal) decimal.Decimal {
	amountOut1 := CalAmountOut1(amountIn, reserveIn1, reserveOut1)
	amountOut2 := CalAmountOut1(amountOut1, reserveIn2, reserveOut2)
	return amountOut2.Sub(amountIn)
}

func CalAmountOut(amountIn decimal.Decimal, reserveIns, reserveOuts []decimal.Decimal) []decimal.Decimal {
	switch len(reserveIns) {
	case 1:
		return []decimal.Decimal{CalAmountOut1(amountIn, reserveIns[0], reserveOuts[0])}
	case 2:
		return CalAmountOut2(amountIn, reserveIns[0], reserveOuts[0], reserveIns[1], reserveOuts[1])
	case 3:
		return CalAmountOut3(amountIn, reserveIns[0], reserveOuts[0], reserveIns[1], reserveOuts[1], reserveIns[2], reserveOuts[2])
	case 4:
		return CalAmountOut4(amountIn, reserveIns[0], reserveOuts[0], reserveIns[1], reserveOuts[1], reserveIns[2], reserveOuts[2], reserveIns[3], reserveOuts[3])
	}
	return []decimal.Decimal{decimal.Zero}
}
