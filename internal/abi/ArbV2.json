[{"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "address", "name": "_WETH", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "InvalidAddress", "type": "error"}, {"inputs": [], "name": "InvalidPath", "type": "error"}, {"inputs": [], "name": "InvalidReserve", "type": "error"}, {"inputs": [], "name": "InvalidTokenA", "type": "error"}, {"inputs": [], "name": "InvalidTokenB", "type": "error"}, {"inputs": [], "name": "NoProfit", "type": "error"}, {"inputs": [], "name": "NotFromPool", "type": "error"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [], "name": "WithdrawFailed", "type": "error"}, {"inputs": [], "name": "ZeroAddress", "type": "error"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [], "name": "WETH", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "selector", "type": "bytes4"}], "name": "addSupportedCallback", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "pool", "type": "address"}, {"internalType": "address", "name": "repayToken", "type": "address"}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256"}, {"internalType": "address", "name": "borrowToken", "type": "address"}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}, {"internalType": "uint8[]", "name": "fees", "type": "uint8[]"}, {"internalType": "uint16[]", "name": "feeRateDenominators", "type": "uint16[]"}, {"internalType": "address[]", "name": "pools", "type": "address[]"}, {"internalType": "address[]", "name": "path", "type": "address[]"}], "internalType": "struct ArbV2.ArbParams", "name": "params", "type": "tuple"}], "name": "arbV2", "outputs": [{"internalType": "uint256", "name": "profit", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"internalType": "address[]", "name": "pools", "type": "address[]"}, {"internalType": "address[]", "name": "paths", "type": "address[]"}, {"internalType": "uint8[]", "name": "fees", "type": "uint8[]"}, {"internalType": "uint16[]", "name": "feeRateDenominators", "type": "uint16[]"}], "name": "getAmountsOut", "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "pool", "type": "address"}, {"internalType": "address", "name": "tokenA", "type": "address"}, {"internalType": "address", "name": "tokenB", "type": "address"}], "name": "getReserves", "outputs": [{"internalType": "uint256", "name": "reserveA", "type": "uint256"}, {"internalType": "uint256", "name": "reserveB", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "selector", "type": "bytes4"}], "name": "removeSupportedCallback", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_sender", "type": "address"}, {"internalType": "uint256", "name": "_amount0", "type": "uint256"}, {"internalType": "uint256", "name": "_amount1", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "uniswapV2Call", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address payable", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdrawETH", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdrawToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}]