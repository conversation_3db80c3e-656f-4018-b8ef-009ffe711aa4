[{"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "address", "name": "_swapRouter", "type": "address"}, {"internalType": "address", "name": "_WETH9", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "NotFromPool", "type": "error"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [], "name": "WETH9", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "pool", "type": "address"}, {"internalType": "address", "name": "borrowToken", "type": "address"}, {"internalType": "int256", "name": "borrowAmount", "type": "int256"}, {"internalType": "address", "name": "repayToken", "type": "address"}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256"}, {"internalType": "bytes", "name": "path", "type": "bytes"}], "internalType": "struct ArbV3.ArbParams", "name": "params", "type": "tuple"}], "name": "arbV3", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "swapRouter", "outputs": [{"internalType": "contract IForgeSwapRouter", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "int256", "name": "amount0Delta", "type": "int256"}, {"internalType": "int256", "name": "amount1Delta", "type": "int256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "uniswapV3SwapCallback", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address payable", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdrawETH", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdrawToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}]